<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>بحث هندسة البرمبت وبناء الوكلاء الذكيين</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="بحث شامل في هندسة البرمبت وبناء الوكلاء الذكيين مع مقارنة بين ChatGPT وGemini وClaude">
    <meta name="keywords" content="هندسة البرمبت, وكلاء ذكيين, ChatGP<PERSON>, <PERSON>, <PERSON>, ذكاء اصطناعي">
    <meta name="author" content="مختبر البرمبت">

    <!-- ربط ملف CSS المنفصل -->
    <link rel="stylesheet" href="styles.css">

    <!-- خطوط Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        /* عام */
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f5f5f5;
            color: #333333;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }
        html {
            scroll-behavior: smooth; /* التمرير السلس */
        }
        header {
            background: linear-gradient(135deg, #4535cd, #81cdf8);
            padding: 20px;
            text-align: center;
            color: #ffffff;
        }
        header h1 {
            margin: 0;
            font-size: 2em;
        }

        /* شريط التنقل */
        nav {
            background-color: #ffffff;
            border-bottom: 1px solid #dddddd;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        nav .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1000px;
            margin: auto;
            padding: 0 20px;
        }
        nav .logo {
            font-weight: bold;
            color: #4535cd;
        }
        nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
        }
        nav ul li {
            margin: 0 15px;
        }
        nav ul li a {
            text-decoration: none;
            color: #4535cd;
            font-weight: bold;
            padding: 8px;
            border-radius: 4px;
        }
        nav ul li a.active {
            background-color: #81cdf8;
            color: #ffffff;
        }

        /* زر قائمة الهواتف */
        .menu-toggle {
            display: none;
            flex-direction: column;
            cursor: pointer;
        }
        .menu-toggle span {
            width: 25px;
            height: 3px;
            background-color: #4535cd;
            margin: 4px 0;
            transition: 0.4s;
        }
        /* عند تفعيل القائمة */
        .menu-toggle.open span:nth-child(1) {
            transform: rotate(-45deg) translate(-5px, 6px);
        }
        .menu-toggle.open span:nth-child(2) {
            opacity: 0;
        }
        .menu-toggle.open span:nth-child(3) {
            transform: rotate(45deg) translate(-5px, -6px);
        }

        /* استجابة الهواتف */
        @media (max-width: 768px) {
            nav ul {
                position: absolute;
                top: 60px;
                right: 0;
                background-color: #ffffff;
                width: 200px;
                flex-direction: column;
                border-left: 1px solid #dddddd;
                border-bottom: 1px solid #dddddd;
                display: none;
            }
            nav ul.show {
                display: flex;
            }
            nav ul li {
                margin: 10px;
            }
            .menu-toggle {
                display: flex;
            }
        }

        .container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 0 20px;
        }
        section {
            background-color: #ffffff;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        section h2 {
            margin: 0;
            padding: 15px 20px;
            background-color: #4535cd;
            color: #ffffff;
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        section .content {
            display: none;
            padding: 20px;
        }
        section .content p,
        section .content ul,
        section .content table {
            margin-bottom: 15px;
        }

        /* أيقونة السهم وتدويرها عند الفتح */
        .toggle-icon {
            transition: transform 0.3s ease;
        }
        .expanded .toggle-icon {
            transform: rotate(90deg);
        }

        /* جداول */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table th, table td {
            border: 1px solid #dddddd;
            padding: 8px;
            text-align: right;
        }
        table th {
            background-color: #4535cd;
            color: #ffffff;
            position: sticky;
            top: 0; /* تثبيت رأس الجدول */
            z-index: 2;
        }
        /* تظليل الصفوف بالتناوب */
        table tbody tr:nth-child(odd) {
            background-color: #f9f9f9;
        }

        /* زر “العودة للأعلى” */
        #backToTop {
            display: none;
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #4535cd;
            color: #ffffff;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            font-size: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(69, 53, 205, 0.3);
        }
        #backToTop:hover {
            background: #3a2ba8;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(69, 53, 205, 0.4);
        }

        /* شريط البحث والفلترة */
        .search-filter-container {
            background: #ffffff;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box {
            flex: 1;
            min-width: 250px;
            padding: 10px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #4535cd;
        }

        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            background: #ffffff;
            font-size: 14px;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: #4535cd;
        }

        /* تحسينات الجداول للموبايل */
        .table-container {
            overflow-x: auto;
            margin: 15px 0;
        }

        @media (max-width: 768px) {
            .search-filter-container {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box, .filter-select {
                min-width: 100%;
            }

            table {
                font-size: 12px;
                min-width: 600px;
            }

            table th, table td {
                padding: 6px;
                word-wrap: break-word;
            }
        }

        /* تحسينات إضافية للتفاعل */
        section h2:hover {
            background-color: #3a2ba8;
            transition: background-color 0.3s ease;
        }

        nav ul li a:hover {
            background-color: #f0f0f0;
            transition: background-color 0.3s ease;
        }

        /* مؤشر التحميل */
        .loading-indicator {
            display: none;
            text-align: center;
            padding: 20px;
            color: #4535cd;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4535cd;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تحسين الطباعة */
        @media print {
            nav, #backToTop, .search-filter-container {
                display: none !important;
            }

            section .content {
                display: block !important;
            }

            body {
                font-size: 12pt;
                line-height: 1.4;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>بحث هندسة البرمبت وبناء الوكلاء الذكيين</h1>
    </header>
    <!-- شريط علوي محسّن -->
    <div class="top-bar">
        <div class="top-bar-container">
            <div class="top-bar-right">
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="ar">العربية</option>
                        <option value="en">English</option>
                        <option value="fr">Français</option>
                    </select>
                </div>
                <div class="theme-toggle">
                    <button id="themeToggle" title="تبديل الوضع المظلم">
                        <span class="theme-icon">🌙</span>
                    </button>
                </div>
                <div class="user-menu">
                    <button class="user-btn" id="userMenuBtn">
                        <span class="user-icon">👤</span>
                        <span class="user-name">ضيف</span>
                        <span class="dropdown-arrow">▼</span>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" class="dropdown-item">تسجيل الدخول</a>
                        <a href="#" class="dropdown-item">إنشاء حساب</a>
                        <a href="#" class="dropdown-item">الملف الشخصي</a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item">الإعدادات</a>
                        <a href="#" class="dropdown-item">المساعدة</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- شريط تنقل محسّن -->
    <nav class="main-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="logo">
                    <span class="logo-icon">🧠</span>
                    <div class="logo-text">
                        <span class="logo-title">مختبر البرمبت</span>
                        <span class="logo-subtitle">Prompt Engineering Lab</span>
                    </div>
                </div>
            </div>

            <!-- قائمة تنقل رئيسية -->
            <div class="nav-menu">
                <div class="nav-category">
                    <span class="category-title">الأساسيات</span>
                    <div class="nav-items">
                        <a href="#section1" class="nav-item" data-tooltip="مقدمة شاملة للبحث">
                            <span class="nav-icon">📋</span>
                            <span class="nav-text">المقدمة</span>
                        </a>
                        <a href="#section2" class="nav-item" data-tooltip="مكونات هندسة البرمبت">
                            <span class="nav-icon">🔧</span>
                            <span class="nav-text">المكونات</span>
                        </a>
                    </div>
                </div>

                <div class="nav-category">
                    <span class="category-title">التحليل والمقارنة</span>
                    <div class="nav-items">
                        <a href="#section3" class="nav-item" data-tooltip="تحليل النماذج الثلاثة">
                            <span class="nav-icon">📊</span>
                            <span class="nav-text">التحليل</span>
                        </a>
                        <a href="#section6" class="nav-item" data-tooltip="نظام المقارنة الآلي">
                            <span class="nav-icon">⚖️</span>
                            <span class="nav-text">المقارنة</span>
                        </a>
                    </div>
                </div>

                <div class="nav-category">
                    <span class="category-title">الأدوات والتطبيقات</span>
                    <div class="nav-items">
                        <a href="#section8" class="nav-item" data-tooltip="أداة توليد البرمبتات">
                            <span class="nav-icon">🤖</span>
                            <span class="nav-text">المولد</span>
                        </a>
                        <a href="#section9" class="nav-item" data-tooltip="أداة التصنيف التلقائي">
                            <span class="nav-icon">🏷️</span>
                            <span class="nav-text">المصنف</span>
                        </a>
                        <a href="#section11" class="nav-item" data-tooltip="لوحة التحكم التفاعلية">
                            <span class="nav-icon">📈</span>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- أزرار إضافية -->
            <div class="nav-actions">
                <button class="action-btn" id="quickSearchBtn" title="البحث السريع (Ctrl+K)">
                    <span class="action-icon">🔍</span>
                </button>
                <button class="action-btn" id="bookmarkBtn" title="إضافة إلى المفضلة">
                    <span class="action-icon">⭐</span>
                </button>
                <button class="action-btn" id="shareBtn" title="مشاركة الصفحة">
                    <span class="action-icon">📤</span>
                </button>
                <button class="action-btn" id="fullscreenBtn" title="ملء الشاشة">
                    <span class="action-icon">⛶</span>
                </button>
            </div>

            <!-- زر القائمة للموبايل -->
            <div class="mobile-menu-toggle" id="mobile-menu">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <div class="container">

        <!-- شريط البحث والفلترة -->
        <div class="search-filter-container">
            <input type="text" class="search-box" id="searchBox" placeholder="ابحث في المحتوى... (مثال: هندسة البرمبت، وكلاء ذكيين، تقييم)">
            <select class="filter-select" id="filterCategory">
                <option value="all">جميع الأقسام</option>
                <option value="prompt">هندسة البرمبت</option>
                <option value="agents">الوكلاء الذكيين</option>
                <option value="evaluation">التقييم والمقارنة</option>
                <option value="tools">الأدوات والتطبيقات</option>
                <option value="database">قواعد البيانات</option>
                <option value="examples">الأمثلة العملية</option>
            </select>
            <select class="filter-select" id="filterComplexity">
                <option value="all">جميع المستويات</option>
                <option value="basic">أساسي</option>
                <option value="intermediate">متوسط</option>
                <option value="advanced">متقدم</option>
            </select>
        </div>

        <!-- مؤشر التحميل -->
        <div class="loading-indicator" id="loadingIndicator">
            <div class="spinner"></div>
            <p>جاري البحث...</p>
        </div>

        <!-- Section 1 -->
        <section id="section1" class="collapsed" data-category="basics" data-complexity="beginner" data-keywords="مقدمة غرض بحث هندسة برمبت وكلاء ذكيين">
            <h2 onclick="toggleSection(this)">
                <span>1. المقدمة والغرض من البحث</span>
                <span class="toggle-icon">▶</span>
            </h2>
            <div class="content">
                <p>
                    تهدف هذه الدراسة إلى استكشاف أفضل الأساليب لكتابة برمبتات فعّالة ولتصميم وكلاء ذكيين لإدارة سير العمل في مجال <strong>{مجال البحث أو التطبيق المستهدف}</strong>،
                    مستفيدين من قدرات كلٍّ من <strong>{النماذج الثلاثة: OpenAI ChatGPT، Google Gemini 2.5، Anthropic Claude 4}</strong>.
                    سنقوم بتحليل أنماط تفاعل كل نموذج مع البرمبتات ودمج أحدث تقنيات هندسة البرمبت (مثل Self-Consistency وTree of Thought وReAct)
                    مع مبادئ بناء الوكلاء (Agent Design Patterns وGuardrails) بغرض الوصول إلى <strong>{النتيجة المستهدفة من الدراسة}</strong>.
                    كما سنستفيد من مصادر أمثلة متنوعة مثل <strong>{مصادر الأمثلة المقترحة: Reddit، GitHub، PaperWithCode، مجتمع OpenAI}</strong>،
                    وسنركّز على سيناريوهات استخدام متعددة مثل <strong>{نوع الاستخدام: تجاري، أكاديمي، برمجي، إلخ}</strong>
                    مع دراسة أنماط برمبت مختلفة مثل <strong>{نمط البرمبتات والوكيل المرغوب دراسته: أوامر مباشرة، حواري، Role Playing، CoT، إلخ}</strong>.
                </p>
            </div>
        </section>

        <!-- Section 2 -->
        <section id="section2" class="collapsed" data-category="basics" data-complexity="intermediate" data-keywords="مكونات هندسة برمبت بنية نبرة تعقيد وضوح تحكم إخراج ضوابط">
            <h2 onclick="toggleSection(this)">
                <span>2. تعريف مكونات هندسة البرمبت الأساسية</span>
                <span class="toggle-icon">▶</span>
            </h2>
            <div class="content">
                <p>في فقرة موحدة، تتألف مكونات هندسة البرمبت من:</p>
                <ul>
                    <li>
                        <strong>البنية (Structure):</strong> تنظيم المعلومات داخل البرمبت عبر:
                        <ul>
                            <li><strong>System Prompt:</strong> تحديد دور النموذج أو الوكيل (مثل: “أنت وكيل دعم فني…”).</li>
                            <li><strong>Role Prompt:</strong> وضع السيناريو (مثل: “تقمص دور أستاذ جامعي…”).</li>
                            <li><strong>Contextual Prompt:</strong> تضمين سياق إضافي أو معلومات خلفية (مثل: “في إطار {نطاق معين}…”).</li>
                            <li>استخدام عناوين رئيسية ونقاط فرعية وتعليمات واضحة لتتبع سير العمل.</li>
                        </ul>
                    </li>
                    <li>
                        <strong>النبرة (Tone):</strong> اختيار اللغة الرسمية مقابل اللغة الودية ومدى تأثير ذلك على استجابة النموذج/الوكيل 
                        (مثل نبرة “رسمية أكاديمية” مقابل نبرة “ودية مبسّطة”).
                    </li>
                    <li>
                        <strong>درجة التعقيد (Complexity):</strong> التحكم في طول الجمل وبُنيتها:
                        <ul>
                            <li>جمل قصيرة وواضحة مقابل جمل طويلة ومعقدة.</li>
                            <li>استخدام تقنيات مثل <strong>Chain of Thought (CoT)</strong> لحث النموذج على “التفكير خطوة بخطوة”، وتقنيات 
                                <strong>Tree of Thought (ToT)</strong> لاستكشاف مسارات تفكير متعددة قبل الوصول للقرار النهائي.</li>
                        </ul>
                    </li>
                    <li>
                        <strong>وضوح الهدف (Clarity of Intent):</strong> التأكد من وجود هدف محدّد وواضح داخل البرمبت، باستخدام عبارات مثل 
                        <em>{وضوح الهدف}</em> (مثال: “قم بتحليل هذه الشيفرة”، “قارن بين هذه السيناريوهات”). وفي تعليمات الوكيل، 
                        تحديد حالات الدخول (Trigger Conditions) وحالات الخروج (Exit Conditions).
                    </li>
                    <li>
                        <strong>التحكم بالإخراج (Output Control):</strong> استخدام عبارات مثل <em>{التحكم بالإخراج}</em> (مثال: “اخرج النتائج في جدول JSON”، 
                        “زوّد أمثلة توضيحية”، “قدم ملخصًا موجزًا في 200 كلمة”). وفي سياق الوكيل، تحديد صيغ قابلة للتحقق (مثال: 
                        “إذا تجاوزت النتيجة حدًّا معيّنًا، أرسل تنبيهًا”).
                    </li>
                    <li>
                        <strong>الضوابط والقيود (Guardrails & Constraints):</strong><br>
                        - <strong>Safety Guardrails:</strong> مصنّف الأمان لمنع الانحراف عن الموضوع أو حقن نصي، مصنّف الصلة لضمان ملاءمة طلب المستخدم، 
                        وفلاتر PII لمنع كشف معلومات حساسة.<br>
                        - في “تعليمات الوكيل”: تضمين شروط تحقق إضافية (مثل: “إذا طُلب إجراء مالي، احرص على التحقق من الهوية أولًا”).
                    </li>
                </ul>
                <p><em>الفقرة النموذجية (قالب):</em></p>
                <p style="background-color:#eef; padding:10px; border-radius:4px;">
                    تتألف مكونات هندسة البرمبت من: <strong>البنية</strong> التي تشمل System Prompt وRole Prompt وContextual Prompt لتنظيم المحتوى بسياق واضح؛
                    <strong>النبرة</strong> التي تحدّد أسلوب الكلام (رسمي أو ودي) ومدى تأثيره في استجابة النموذج/الوكيل؛ 
                    <strong>درجة التعقيد</strong> التي يمكن التحكم فيها عبر تقنيات Chain of Thought وTree of Thought لتحفيز النموذج على سلسلة منطقية متعددة المسارات 
                    قبل الخروج بالقرار؛ <strong>وضوح الهدف</strong> بواسطة تعليمات دقيقة كـ <em>{وضوح الهدف}</em> تضمن تحديد المهمة بعبارات فعلية
                    (“حلّ”، “قارن”، “استخرج”)، وضمن “تعليمات الوكيل” تتضمّن حالات الدخول وحالات الخروج بوضوح. أما 
                    <strong>التحكم بالإخراج</strong> (<em>Output Control</em>) فيُحقق عبر عبارات مثل <em>{التحكم بالإخراج}</em> لتحديد الشكل المطلوب 
                    للإجابة (جدول JSON، ملخص نصي، كود برمجي مع تعليقات). وأخيرًا، قليلة الاستخدام في Prompt Engineering الأصلية 
                    هو <strong>الضوابط والقيود</strong> (<em>Guardrails</em>) التي تتضمّن مصنّف الأمان لمنع حقن نصي، ومصنّف الصلة لضمان ملاءمة الاستفسار، 
                    وفلاتر لحماية البيانات الشخصية (PII). في سياق بناء الوكلاء (Agent Design)، يتم تضمين شروط تحقق إضافية مثل التحقق من الهوية قبل تنفيذ أي 
                    إجراء مالي أو طبي.
                </p>
            </div>
        </section>

        <!-- Section 3 -->
        <section id="section3" class="collapsed" data-category="analysis" data-complexity="advanced" data-keywords="تحليل استجابات نماذج ChatGPT Gemini Claude وكلاء مقارنة أداء">
            <h2 onclick="toggleSection(this)">
                <span>3. تحليل استجابات النماذج الثلاثة ودمج مبادئ بناء الوكلاء</span>
                <span class="toggle-icon">▶</span>
            </h2>
            <div class="content">
                <p>فقرة مقارنة عامة بين قدرات كل نموذج وإضافة ملاحظات Agent Design:</p>
                <ul>
                    <li>
                        <strong>OpenAI ChatGPT:</strong>
                        <ul>
                            <li>يتفوق في تفسير البرمبتات التفصيلية والأسلوب الحواري، ويظهر قدرة عالية على تنفيذ <em>مهام متعددة الخطوات</em> بفضل استخدام Chain of Thought.</li>
                            <li>يدعم استراتيجيات مثل <strong>Self-Consistency</strong> لتحسين الثبات في الإجابات عند الطلب مرارًا وتكرارًا.</li>
                            <li>يمكن توظيفه كنظام <strong>Single-Agent</strong> حيث يقوم بجميع الاستدعاءات داخليًا دون الحاجة إلى وكلاء فرعيين.</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Google Gemini 2.5:</strong>
                        <ul>
                            <li>يتجاوب بسرعة مع الأسئلة المباشرة، ويقدم إجابات مختصرة مناسبة لـ<em>خطوات الوكيل البسيط</em> التي لا تتطلب مسارات فكرية عديدة.</li>
                            <li>أقل مرونة في استراتيجيات <em>Tree of Thought</em>، ويمكن استخدامه كـ <em>Helper-Agent</em> في نظام 
                                <strong>Multi-Agent</strong> لتنفيذ مهام فرعية بسيطة مثل استرجاع المعلومات ثم إعادتها إلى الوكيل المركزي.</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Anthropic Claude 4:</strong>
                        <ul>
                            <li>يتفوق في معالجة السياقات الأخلاقية والفلسفية، ويقدم وعيًا سياقيًا مرتفعًا في المواضيع الحساسة، مما يجعله مناسبًا لأن يكون 
                                <em>Guardian Agent</em> ضمن طبقة <strong>Guardrails</strong> (للتحقق من الاتساق الأخلاقي لكل قرار).</li>
                            <li>يدعم استراتيجيات <em>ReAct</em> (التفكير + اتخاذ إجراء)، مما يسمح له بالتفاعل مع أدوات خارجية مثل قواعد بيانات أو APIs.</li>
                        </ul>
                    </li>
                </ul>
                <p>شرح نقاط القوة والضعف لكل نموذج وفق المعايير التالية:</p>
                <table>
                    <thead>
                        <tr>
                            <th>النموذج</th>
                            <th>الدقة (Accuracy)</th>
                            <th>الإبداع (Creativity)</th>
                            <th>السرعة (Latency)</th>
                            <th>قابلية التحكم (Controllability)</th>
                            <th>مهام متعددة الخطوات (Multi-Step)</th>
                            <th>دور الوكيل (Agent Role)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>OpenAI ChatGPT</td>
                            <td>عالية بفضل CoT وSelf-Consistency</td>
                            <td>إبداعي ومناسب للنصوص المعقدة</td>
                            <td>متوسط إلى مرتفع في المهمات المعقدة</td>
                            <td>ممتاز (System Prompt مفصل)</td>
                            <td>متميز في تتبع التسلسل والخطوات الوسيطة</td>
                            <td>Single-Agent / Manager-Agent</td>
                        </tr>
                        <tr>
                            <td>Google Gemini 2.5</td>
                            <td>جيدة في الاستفسارات العامة وأقل في المتخصصة</td>
                            <td>واضح وسريع، لكن أقل ابتكارًا</td>
                            <td>منخفض (سريع جدًا للمباشر)</td>
                            <td>جيد للتعليمات المبسطة، لكن أقل مرونة</td>
                            <td>محدود في المهام المعقدة، مناسب للمهام الفرعية</td>
                            <td>Helper-Agent ضمن Multi-Agent</td>
                        </tr>
                        <tr>
                            <td>Anthropic Claude 4</td>
                            <td>عالية خصوصًا في السياقات الأخلاقية</td>
                            <td>تنوّعات لغوية عميقة وفلسفية</td>
                            <td>مرتفع نسبيًا بسبب طول التفاصيل</td>
                            <td>قوي جدًا في تضمين خطوط أخلاقية وقيمية</td>
                            <td>جيد لكنه يميل للتوسع في كل خطوة</td>
                            <td>Guardian-Agent ضمن Multi-Agent</td>
                        </tr>
                    </tbody>
                </table>
                <p>ملخص مقارن بصيغة جدول مع مثال توضيحي لاختلاف النتائج عند استخدام برمبت محدّد:</p>
                <table>
                    <thead>
                        <tr>
                            <th>النموذج</th>
                            <th>Prompt Example</th>
                            <th>النتيجة (ملخص)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>ChatGPT</td>
                            <td>“اكتب تقريرًا تعليميًا حول {موضوع} مستخدمًا CoT وإبراز الاعتبارات الأخلاقية.”</td>
                            <td>
                                شرح تفصيلي منظم خطوة بخطوة، يأخذ بعين الاعتبار النقاط الأخلاقية دوماً، 
                                يقدم نتائج متسقة عند التكرار.
                            </td>
                        </tr>
                        <tr>
                            <td>Gemini 2.5</td>
                            <td>“اكتب تقريرًا تعليميًا حول {موضوع} مستخدمًا CoT…”</td>
                            <td>
                                إجابة واقتضابية، يقدّم النقاط الأساسية بسرعة لكن بدون عمق كبير في كل خطوة، 
                                قد يهمل بعض التفاصيل الأخلاقية.
                            </td>
                        </tr>
                        <tr>
                            <td>Claude 4</td>
                            <td>“اكتب تقريرًا تعليميًا حول {موضوع} مستخدمًا CoT…”</td>
                            <td>
                                تحليل عميق، يركز على الاعتبارات الأخلاقية والفلسفية، يعرض التفصيل بروح تأملية، 
                                لكنه أطول وقد يستغرق وقتًا أكبر.
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Section 4 -->
        <section id="section4" class="collapsed" data-category="comparison" data-complexity="intermediate" data-keywords="جمع تصنيف أمثلة برمبت وكلاء إنترنت مجتمعات Reddit GitHub">
            <h2 onclick="toggleSection(this)">
                <span>4. جمع وتصنيف أمثلة برمبت ووكلاء من الإنترنت والمجتمعات</span>
                <span class="toggle-icon">▶</span>
            </h2>
            <div class="content">
                <p>جمع قائمة مبدئية من أمثلة برمبت فعّالة وAgent Designs من المصادر التالية:</p>
                <ul>
                    <li>Reddit: r/PromptEngineering، r/ChatGPTAgents</li>
                    <li>مستودعات GitHub: “prompts collection”، “agent-prompts”، “AI-Agent-Playbook”</li>
                    <li>مواقع أوراق بحثية وأكاديمية: PapersWithCode (بحث عن “Chain of Thought”، “Tree of Thought”، “ReAct Agents”)</li>
                    <li>مجتمعات OpenAI الرسمية ومدونات Google AI وAnthropic</li>
                </ul>
                <p>جدول أولي لأمثلة (5–10 سجلات) مع الحقول المطلوبة:</p>
                <table>
                    <thead>
                        <tr>
                            <th>Prompt_ID</th>
                            <th>Prompt_Text / Agent_Description</th>
                            <th>Prompt_Type</th>
                            <th>Agent_Pattern</th>
                            <th>Application_Domain</th>
                            <th>Complexity_Level</th>
                            <th>Initial_Performance_Notes</th>
                            <th>Source</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>“اكتب مقالًا تعليميًا عن {موضوع} مستخدمًا Chain of Thought وإبراز الاعتبارات الأخلاقية.”</td>
                            <td>Chain of Thought</td>
                            <td>Single-Agent</td>
                            <td>تعليم</td>
                            <td>متقدم</td>
                            <td>ChatGPT: شرح تفصيلي؛ Gemini: مختصر؛ Claude: إضافة أخلاقية</td>
                            <td>Reddit r/PromptEngineering</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>“Create an agent in Python that uses ReAct to search a database for {item} ثم يعيد النتيجة مُرتّبة.”</td>
                            <td>ReAct + Agent Code</td>
                            <td>Manager-Agent</td>
                            <td>برمجة</td>
                            <td>متقدم</td>
                            <td>ChatGPT: كود مفصّل؛ Gemini: كود مبسّط؛ Claude: توضيح للأخلاقيات</td>
                            <td>GitHub AI-Agent-Playbook</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>“Provide a financial summary for Q2 in {sector} with table in JSON and ethical notes.”</td>
                            <td>Few-Shot</td>
                            <td>Helper-Agent</td>
                            <td>مالية</td>
                            <td>متوسط</td>
                            <td>ChatGPT: تفصيلي؛ Gemini: سريع؛ Claude: مفصّل أخلاقي</td>
                            <td>PaperWithCode</td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>“صمّم وكيلًا في Python يجيب على استفسارات طبية ثم يحولها إلى إشعارات عاجلة للمختصين.”</td>
                            <td>Agent Code</td>
                            <td>Decentralized</td>
                            <td>صحّة</td>
                            <td>متقدم</td>
                            <td>ChatGPT: كود توضحي؛ Gemini: مقتضب؛ Claude: إضافة نقاش شرعي</td>
                            <td>OpenAI Agents Docs</td>
                        </tr>
                        <tr>
                            <td>5</td>
                            <td>“اكتب قصيدة فلسفية قصيرة عن الهوية في عالم متسارع مستخدمًا Tree of Thought.”</td>
                            <td>Tree of Thought</td>
                            <td>Single-Agent</td>
                            <td>إبداعية</td>
                            <td>متوسط</td>
                            <td>ChatGPT: سرد تحليلي؛ Gemini: سرد سريع؛ Claude: عمق فلسفي</td>
                            <td>Reddit r/CreativePrompts</td>
                        </tr>
                    </tbody>
                </table>
                <p>ملاحظة عامة عن كل مثال (فقرة قصيرة توضح سبب فعاليته وملاءمته للنموذج/الوكيل):</p>
                <ul>
                    <li>هذا البرمبت يعمل كـ Single-Agent مع Chain of Thought، فعّال لـ ChatGPT لأنه يقدّم سياقًا واضحًا وتحليلًا تفصيليًا، أما عند استخدامه كـ Helper-Agent مع Gemini 2.5، كانت النتائج أسرع لكنها أقل عمقًا.</li>
                    <li>البرمبت الثاني يوضح تصميم وكيل ReAct؛ مفيد لـ ChatGPT لإخراج كود مفصّل، بينما Gemini يقدم إجابة مختصرة، وClaude يُضيف اعتبارات أخلاقية للقواعد الطبية.</li>
                    <li>البرمبت الثالث يختبر قدرة الوكالات المالية؛ ChatGPT ينتج ملخصًا موسعًا مع JSON مناسب، وGemini يعيد جدولًا بسيطًا، وClaude يضمن ملاحظات أخلاقية حول تقارير مالية.</li>
                    <li>البرمبت الرابع يُظهر بنية وكيل طبي؛ ChatGPT يخرج مثالًا برمجيًا واضحًا، وGemini يقتصر على المهمة بسرعة، بينما Claude يفحص المسائل الشرعية والأخلاقية للأوامر الطبية.</li>
                    <li>البرمبت الخامس يجمع بين ToT والإبداع؛ ChatGPT يولّد قصيدة مع فروع سردية، وGemini يقدّم قصيدة مختصرة، وClaude يضيف عمق فلسفي ويتجنب المحتوى المرفوض.</li>
                </ul>
            </div>
        </section>

        <!-- Section 5 -->
        <section id="section5" class="collapsed" data-category="tools" data-complexity="advanced" data-keywords="قاعدة بيانات برمبتات وكلاء مصنفة هيكل جدول schema">
            <h2 onclick="toggleSection(this)">
                <span>5. بناء “قاعدة بيانات برمبتات ووكلاء” مصنّفة</span>
                <span class="toggle-icon">▶</span>
            </h2>
            <div class="content">
                <p><strong>1. هيكل الجدول (Database Schema):</strong></p>
                <ul>
                    <li><strong>Prompt_ID:</strong> معرف فريد</li>
                    <li><strong>Prompt_Text:</strong> نص البرمبت</li>
                    <li><strong>Prompt_Type:</strong> نوع البرمبت (Zero-Shot, Few-Shot, CoT, ToT, ReAct)</li>
                    <li><strong>Agent_Pattern:</strong> نمط الوكيل (Single-Agent, Manager-Agent, Helper-Agent, Decentralized Agent)</li>
                    <li><strong>Domain:</strong> المجال التطبيقي (تعليم، برمجة، صحّة، مالية…)</li>
                    <li><strong>Complexity_Level:</strong> مستوى التعقيد (بسيط، متوسط، متقدم)</li>
                    <li><strong>Source:</strong> المصدر (Reddit، GitHub، ورقة بحثية…)</li>
                    <li><strong>Initial_Performance_Notes:</strong> ملاحظات حول أداء النماذج والوكيل</li>
                    <li><strong>Guardrail_Settings:</strong> إعدادات الضوابط المطبّقة (JSON: {Temperature, Top_P, Safety, PII_Filter})</li>
                    <li><strong>Continual_Learning_Flag:</strong> مؤشر إذا كان الوكيل يدعم التعلم المستمر (True/False)</li>
                    <li><strong>Timestamp:</strong> وقت الإدخال</li>
                    <li><strong>Version:</strong> رقم إصدار الوكيل أو البرمبت</li>
                </ul>

                <p><strong>2. آلية تحديث قاعدة البيانات آليًا:</strong></p>
                <ul>
                    <li>استخدام سكربت Python يجمع أمثلة من JSON-Responses عبر APIs (Reddit API، GitHub API)، ثم يدرجها في الجدول.</li>
                    <li>إضافة حقول <strong>Timestamp</strong> و<strong>Version</strong>.</li>
                </ul>

                <p><strong>3. اقتراح منصّات للتحكم في قاعدة البيانات:</strong></p>
                <ul>
                    <li><strong>Airtable:</strong> جدول تفاعلي سريع بواجهة رسومية.</li>
                    <li><strong>Google Sheets:</strong> سهولة مشاركة وتعاون مع App Scripts لأتمتة التحديث.</li>
                    <li><strong>قاعدة بيانات SQL:</strong> MySQL أو PostgreSQL لمهام بحجم كبير واستعلامات معقدة (مثل استخراج إحصائيات Guardrail_Failures).</li>
                </ul>

                <p><strong>مثال عملي على إدخال 5 سجلات في القاعدة:</strong></p>
                <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Prompt_ID</th>
                            <th>Prompt_Text</th>
                            <th>Prompt_Type</th>
                            <th>Agent_Pattern</th>
                            <th>Domain</th>
                            <th>Complexity_Level</th>
                            <th>Source</th>
                            <th>Initial_Performance_Notes</th>
                            <th>Guardrail_Settings</th>
                            <th>Continual_Learning_Flag</th>
                            <th>Timestamp</th>
                            <th>Version</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>“اكتب مقالًا تعليميًا عن {موضوع} مستخدمًا Chain of Thought وإبراز الاعتبارات الأخلاقية.”</td>
                            <td>Chain of Thought</td>
                            <td>Single-Agent</td>
                            <td>تعليم</td>
                            <td>متقدم</td>
                            <td>Reddit r/PromptEngineering</td>
                            <td>ChatGPT: تفصيلي؛ Gemini: مختصر؛ Claude: أخلاقي</td>
                            <td>{ "Temperature": 0.7, "Top_P": 0.9, "Safety": true }</td>
                            <td>True</td>
                            <td>2025-06-02T10:00:00Z</td>
                            <td>v1.0-AgentCoT</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>“Create an agent in Python that uses ReAct to search a database for {item} ثم يعيد النتيجة مُرتّبة.”</td>
                            <td>ReAct + Agent</td>
                            <td>Manager-Agent</td>
                            <td>برمجة</td>
                            <td>متقدم</td>
                            <td>GitHub AI-Agent-Playbook</td>
                            <td>ChatGPT: كود مفصّل؛ Gemini: مبسّط؛ Claude: أخلاقي</td>
                            <td>{ "Temperature": 0.5, "Top_P": 0.8, "Safety": true }</td>
                            <td>False</td>
                            <td>2025-06-02T10:05:00Z</td>
                            <td>v1.0-AgentReAct</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>“Provide a financial summary for Q2 in {sector} with table in JSON and ethical notes.”</td>
                            <td>Few-Shot</td>
                            <td>Helper-Agent</td>
                            <td>مالية</td>
                            <td>متوسط</td>
                            <td>PaperWithCode</td>
                            <td>ChatGPT: تفصيلي؛ Gemini: سريع؛ Claude: مفصّل أخلاقي</td>
                            <td>{ "Temperature": 0.6, "Top_P": 0.85, "Safety": true }</td>
                            <td>False</td>
                            <td>2025-06-02T10:10:00Z</td>
                            <td>v1.0-FinancialSummary</td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>“صمّم وكيلًا في Python يجيب على استفسارات طبية ثم يحولها إلى إشعارات عاجلة للمختصين.”</td>
                            <td>Agent Code</td>
                            <td>Decentralized</td>
                            <td>صحّة</td>
                            <td>متقدم</td>
                            <td>OpenAI Agents Docs</td>
                            <td>ChatGPT: كود توضحي؛ Gemini: مقتضب؛ Claude: نقاش شرعي</td>
                            <td>{ "Temperature": 0.6, "Top_P": 0.9, "Safety": true, "PII_Filter": true }</td>
                            <td>True</td>
                            <td>2025-06-02T10:15:00Z</td>
                            <td>v1.0-MedicalAgent</td>
                        </tr>
                        <tr>
                            <td>5</td>
                            <td>“اكتب قصيدة فلسفية قصيرة عن الهوية في عالم متسارع مستخدمًا Tree of Thought.”</td>
                            <td>Tree of Thought</td>
                            <td>Single-Agent</td>
                            <td>إبداعية</td>
                            <td>متوسط</td>
                            <td>Reddit r/CreativePrompts</td>
                            <td>ChatGPT: سرد تحليلي؛ Gemini: سرد سريع؛ Claude: عمق فلسفي</td>
                            <td>{ "Temperature": 0.8, "Top_P": 0.95, "Safety": true }</td>
                            <td>False</td>
                            <td>2025-06-02T10:20:00Z</td>
                            <td>v1.0-PoetryAgent</td>
                        </tr>
                    </tbody>
                </table>
                </div>
            </div>
        </section>

        <!-- Section 6 -->
        <section id="section6" class="collapsed" data-category="evaluation" data-complexity="advanced" data-keywords="نظام مقارنة آلي تقييم معايير دقة إبداع وضوح كفاءة">
            <h2 onclick="toggleSection(this)">
                <span>6. تصميم نظام مقارنة آلي (Automated Comparison & Evaluation System)</span>
                <span class="toggle-icon">▶</span>
            </h2>
            <div class="content">
                <p><strong>معايير التقييم الموصى بها:</strong></p>
                <ol>
                    <li><strong>الدقة (Accuracy):</strong> مدى صحة المعلومات مقارنة بالمرجع.</li>
                    <li><strong>الإبداع (Creativity):</strong> قدرة النموذج على تقديم أفكار جديدة.</li>
                    <li><strong>الوضوح (Clarity):</strong> سهولة فهم النص وتنظيمه.</li>
                    <li><strong>الكفاءة (Efficiency):</strong> طول الاستجابة وملاءمتها دون حشو زائد.</li>
                    <li><strong>الامتثال (Compliance):</strong> مدى التزام النموذج بالتعليمات والضوابط الأخلاقية.</li>
                    <li><strong>التوافق الأخلاقي (Ethical Alignment):</strong> مدى احترام الإجابة للضوابط الأخلاقية.</li>
                    <li><strong>سرعة الوكيل (Agent Latency):</strong> الزمن اللازم لاستجابة الوكيل.</li>
                    <li><strong>التعامل مع تعدّد الوكلاء (Multi-Agent Coordination):</strong> مدى نجاح تنسيق الوكلاء إذا وُجد Manager-Agent وHelper-Agents.</li>
                </ol>

                <p><strong>آلية جمع الآراء والاستجابات:</strong></p>
                <ol>
                    <li>استدعاءات API لكل نموذج/وكيل (OpenAI API لـChatGPT، Google Vertex AI/Gemini API، Anthropic API) ضمن “مهام الوكيل”.</li>
                    <li>تخزين الاستجابات في هيكل بيانات (JSON أو قاعدة بيانات SQL) بالحقول:
                        <ul>
                            <li>Prompt_ID</li>
                            <li>Model/Agent_Name</li>
                            <li>Response_Text</li>
                            <li>Timestamp</li>
                            <li>Agent_Role (Manager-Agent، Helper-Agent، Guardian-Agent)</li>
                            <li>Tool_Invocations (مثل: DB Query، HTTP Request، JSON Parse)</li>
                        </ul>
                    </li>
                </ol>

                <p><strong>آلية التقييم الأوتوماتيكي (Meta-Evaluation):</strong></p>
                <ol>
                    <li>استخدام نموذج ثانٍ (Meta-Model) لتقييم كل استجابة:
                        <ul>
                            <li>مثال: “قيّم هذه الإجابة على مقياس 1–5 من حيث الدقة.” ثم تكرار لعدة معايير.</li>
                        </ul>
                    </li>
                    <li>تقنيات <strong>Self-Consistency:</strong> إرسال الاستجابة عدة مرات بدرجات حرارة مختلفة، ثم اختيار الإجابة الأكثر اتساقًا.</li>
                    <li><strong>التقييم الأخلاقي (Ethical Check):</strong> إرسال الاستجابة إلى Claude 4 أو نموذج تصنيف أخلاقي (مثل BERT-ETHICS) للتحقق من الالتزام القيمي.</li>
                    <li>تسجيل نتائج التقييم في حقل جديد بالقاعدة مثل:
                        <pre style="background:#eee; padding:10px; border-radius:4px;">
{
  "Prompt_ID": 1,
  "Agent_Name": "ChatGPT_SingleAgent_v1",
  "Response_Text": "...",
  "Timestamp": "...",
  "Ratings": {
    "Accuracy": 5,
    "Creativity": 4,
    "Clarity": 5,
    "Efficiency": 4,
    "Compliance": 5,
    "EthicalAlignment": 5,
    "AgentLatency_ms": 1200,
    "MultiAgentCoordinationScore": null
  }
}
                        </pre>
                    </li>
                    <li>حساب المتوسطات لكل معيار عبر جميع الوكلاء لتوليد تقرير نهائي يقارن الأداء.</li>
                </ol>

                <p><strong>سير العمل (Workflow) مفصّل:</strong></p>
                <ol>
                    <li>
                        <strong>الخطوة 1:</strong> جلب قائمة البرمبتات/تعليمات الوكيل من قاعدة البيانات (Prompt_ID، Prompt_Text، Agent_Pattern).
                    </li>
                    <li>
                        <strong>الخطوة 2 (استدعاء النماذج والوكيل):</strong>
                        <ul>
                            <li>إذا كان Agent_Pattern = “Single-Agent”، استدعِ النموذج مباشرةً (ChatGPT) مع “Prompt_Text” → احصل على Response_Text_ChatGPT.</li>
                            <li>إذا كان Agent_Pattern = “Manager-Agent”، استدعِ Manager-Agent (مثل ChatGPT_Manager) ليقود حلقات استدعاء إلى Helper-Agents (مثل Gemini 2.5_Helper) لتنفيذ مهام فرعية → احصل على Response_Text_Manager.</li>
                            <li>إذا كان Agent_Pattern = “Helper-Agent”، انتظر استدعاء المدير ثم اعمل المهمة المعينة (مثال: Database Query أو JSON Parsing) → احصل على Response_Text_Helper.</li>
                            <li>إذا كان Agent_Pattern = “Guardian-Agent”، استدعِ Claude 4 للتحقق الأخلاقي → احصل على Response_Text_Guardian.</li>
                            <li>خزن النتائج وسجل زمن الاستجابة (AgentLatency_ms) وسيناريوهات تنسيق الوكلاء (MultiAgentCoordination).</li>
                        </ul>
                    </li>
                    <li>
                        <strong>الخطوة 3 (Meta-Evaluation):</strong>
                        <ul>
                            <li>أرسل كل إجابة إلى ChatGPT أو Claude لتقييم Accuracy، Creativity، Clarity، Efficiency، Compliance.</li>
                            <li>أرسلها إلى نموذج أخلاقي (مثل Claude 4 أو BERT-ETHICS) لتقييم EthicalAlignment.</li>
                            <li>إذا Agent_Pattern = “Manager-Agent”، قيّم مستوى تنسيق الوكلاء عبر معيار Coordination (مقارنة توقيتات واستجابات Helper-Agents).</li>
                        </ul>
                    </li>
                    <li>
                        <strong>الخطوة 4:</strong> تحديث قاعدة البيانات بحقل “Ratings” لكل سجل استجابة (JSON يحتوي على نتائج التقييم).
                    </li>
                    <li>
                        <strong>الخطوة 5:</strong> بعد معالجة جميع السجلات، توليد تقرير مقارنة يوضح متوسط الدرجات لكل معيار لكل نموذج/وكيل، واستنتاج النموذج/الوكيل الأنسب لكل سيناريو.
                    </li>
                </ol>
            </div>
        </section>

        <!-- Section 7 -->
        <section id="section7" class="collapsed" data-category="prompt" data-complexity="intermediate" data-keywords="أساليب صياغة برمبت وكيل موجه أمر مباشر حواري أدوار">
            <h2 onclick="toggleSection(this)">
                <span>7. اختبار أساليب مختلفة في صياغة البرمبت وإنشاء وكيل موجه</span>
                <span class="toggle-icon">▶</span>
            </h2>
            <div class="content">
                <p>نقترح أربعة أساليب متنوعة لصياغة برمبت واحد محدّد (مثال: “اكتب مقالًا حول {موضوع}”) مع “سياق الوكيل” لكل حالة:</p>
                <ol>
                    <li>
                        <strong>أمر مباشر (Direct Instruction):</strong>
                        <pre style="background:#eef; padding:10px; border-radius:4px;">
“اكتب مقالًا من 500 كلمة حول {موضوع} يوضّح النقاط التالية: … استخدم Chain of Thought لشرح المنطق.”
تعليمات الوكيل: “هذه المهمة تُنفّذ بمثابة Single-Agent؛ إذا استلزم الأمر استدعاء قاعدة بيانات، استدعِ Gemini 2.5_Helper.”
                        </pre>
                    </li>
                    <li>
                        <strong>أسلوب حواري (Conversational):</strong>
                        <pre style="background:#eef; padding:10px; border-radius:4px;">
“أنا باحث أعمل على دراسة {مجال_البحث}. كيف يمكنني صياغة برمبت لكتابة مقال حول {موضوع} يتناسب مع ChatGPT وتضمّن خطوات CoT؟”
تعليمات الوكيل: “إذا سأل المستخدم عن مصادر خارجية، اعرض خيار استخدام Google Search API بواسطة Claude 4_Guardian.”
                        </pre>
                    </li>
                    <li>
                        <strong>أسلوب الأدوار (Role-Playing):</strong>
                        <pre style="background:#eef; padding:10px; border-radius:4px;">
“تقمّص دور أستاذ جامعي في مجال {مجال_البحث} وعلّمني كيفية كتابة مقال أكاديمي حول {موضوع} مع توضيح بنية البحث والخطوات المنطقية (Tree of Thought).”
تعليمات الوكيل: “إذا تطلب الأمر التحقق من استشهادات، استخدم ChatGPT للتوليد المبدئي ثم استدعِ Gemini 2.5_Helper لجمع المراجع من PubMed API.”
                        </pre>
                    </li>
                    <li>
                        <strong>مزيج الحوافز (Chain-of-Thought Prompting):</strong>
                        <pre style="background:#eef; padding:10px; border-radius:4px;">
“فكر بصوتٍ عالٍ: بدايةً ما هي النقاط الأساسية لمقال حول {موضوع}، ثم قم بصياغتها في فقرة مُنسّقة واعرض ثلاث بدائل للمقدمة.”
تعليمات الوكيل: “عند الانتهاء من تنفيذ CoT، اختبر Claude 4_Guardian لنقاط هل توجد اعتبارات أخلاقية أو قيود تنظيمية في المحتوى.”
                        </pre>
                    </li>
                </ol>

                <p><strong>تجربة الأساليب الأربع على النماذج الثلاثة وتسجيل الملاحظات:</strong></p>
                <table>
                    <thead>
                        <tr>
                            <th>الأسلوب</th>
                            <th>النموذج</th>
                            <th>Agent_Pattern</th>
                            <th>التفصيل (Depth)</th>
                            <th>السرعة (Latency)</th>
                            <th>الوضوح (Clarity)</th>
                            <th>التوافق الأخلاقي (EthicalAlignment)</th>
                            <th>توافق الوكيل (Agent Compliance)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>A. Direct Instruction</td>
                            <td>ChatGPT</td>
                            <td>Single-Agent</td>
                            <td>عالٍ</td>
                            <td>متوسط</td>
                            <td>ممتاز</td>
                            <td>جيد</td>
                            <td>متوافق</td>
                        </tr>
                        <tr>
                            <td>B. Conversational</td>
                            <td>Gemini 2.5</td>
                            <td>Helper-Agent</td>
                            <td>متوسط</td>
                            <td>ممتاز</td>
                            <td>جيد</td>
                            <td>ضعيف</td>
                            <td>يحتاج ضبط</td>
                        </tr>
                        <tr>
                            <td>C. Role-Playing</td>
                            <td>Claude 4</td>
                            <td>Guardian-Agent</td>
                            <td>عالٍ</td>
                            <td>منخفض</td>
                            <td>ممتاز</td>
                            <td>ممتاز</td>
                            <td>متوافق</td>
                        </tr>
                        <tr>
                            <td>D. Chain of Thought (CoT)</td>
                            <td>ChatGPT + Claude 4</td>
                            <td>Multi-Agent</td>
                            <td>عالٍ جدًا</td>
                            <td>منخفض جدًا</td>
                            <td>ممتاز</td>
                            <td>ممتاز</td>
                            <td>متوافق</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Section 8 -->
        <section id="section8" class="collapsed" data-category="tools" data-complexity="advanced" data-keywords="أداة توليد أوتوماتيكي برمبتات محترفة وكلاء Python سكربت">
            <h2 onclick="toggleSection(this)">
                <span>8. إنشاء أداة توليد أوتوماتيكي لبرمبتات محترفة وتوجيه الوكلاء</span>
                <span class="toggle-icon">▶</span>
            </h2>
            <div class="content">
                <p>وصف الخطوات لبناء سكربت Python يُنشئ برمبتات ويوجه الوكلاء:</p>
                <ol>
                    <li>استقبال إدخال بسيط من المستخدم (قالب أو مواصفات عامة لبرمبت).</li>
                    <li>إرسال الطلب إلى ChatGPT أو Claude لاقتراح صياغات محسّنة تلقائيًا.</li>
                    <li>استرجاع قائمة بصيغ برمبت متعددة مرتبة حسب تقييم داخلي (Accuracy، Creativity، EthicalAlignment).</li>
                    <li>تمكين المستخدم من اختيار الصيغة المناسبة ثم حفظها في قاعدة بيانات البرمبتات المصنّفة.</li>
                    <li>توليد “تعليمات الوكيل” تلقائيًا بناءً على الصيغة المختارة (Guardrail_Settings، Agent_Pattern، Tool_Use، Continual_Learning).</li>
                </ol>
                <p>مثال كود Python مبسط:</p>
                <pre style="background:#eee; padding:10px; border-radius:4px;">
import openai
import requests

openai.api_key = "YOUR_API_KEY"

def generate_prompts(input_spec):
    messages = [
        {"role": "system", "content": "أنت سكربت توليد برمبتات متقدّم."},
        {"role": "user", "content": f"اقترح 3 صياغات لبناء وكيل صحي يجيب على استفسارات المرضى حول {input_spec}، مع تضمين Chain of Thought وفلترة PII."}
    ]
    response = openai.ChatCompletion.create(
        model="gpt-4o-mini",
        messages=messages,
        temperature=0.7
    )
    return response.choices[0].message.content  # يحتوي على 3 صياغات وتقييماتها

def evaluate_prompt(prompt_text):
    messages = [
        {"role": "system", "content": "أنت مساعد لتقييم صياغات البرمبتات."},
        {"role": "user", "content": f"قيّم هذه الصياغة من 1 إلى 5 من حيث الدقة، الإبداع، الوضوح، الكفاءة، الالتزام الأخلاقي:\n\"{prompt_text}\""}
    ]
    response = openai.ChatCompletion.create(
        model="gpt-4o-mini",
        messages=messages,
        temperature=0
    )
    return response.choices[0].message.content  # يحتوي على تقييم رقمي لكل معيار

def generate_agent_instructions(prompt_choice):
    # تبسيط مثال: توليد تعليمات وكيل بناءً على الصياغة المختارة
    instructions = {
        "Agent_Role": "HealthcareSupportAgent",
        "Agent_Pattern": "Single-Agent",
        "Guardrail_Settings": {
            "Temperature": 0.6,
            "Top_P": 0.85,
            "Safety": True,
            "PII_Filter": True
        },
        "Tool_Use": ["HealthcareDB_API", "MedicationInfo_API"],
        "Continual_Learning": True
    }
    return instructions

# أمثلة استخدام:
user_input = "السكري المزمن"
prompts_list = generate_prompts(user_input)
print(prompts_list)

chosen_prompt = "الصياغة الثانية من القائمة"  # يختارها المستخدم
eval_result = evaluate_prompt(chosen_prompt)
agent_cfg = generate_agent_instructions(chosen_prompt)
print("تقييم الصياغة:", eval_result)
print("تعليمات الوكيل الناتجة:", agent_cfg)
                </pre>
            </div>
        </section>

        <!-- Section 9 -->
        <section id="section9" class="collapsed" data-category="tools" data-complexity="advanced" data-keywords="أداة تصنيف تلقائي برمبتات وكيل BERT نموذج تصنيفي">
            <h2 onclick="toggleSection(this)">
                <span>9. إنشاء أداة تصنيف تلقائي (Auto-Classifier) للبرمبتات والوكيل</span>
                <span class="toggle-icon">▶</span>
            </h2>
            <div class="content">
                <p><strong>وصف آلية بناء نموذج تصنيفي (Classifier):</strong></p>
                <ol>
                    <li>أخذ نص برمبت أو “تعليمات وكيل” كنص دخول.</li>
                    <li>تصنيفه إلى فئات:
                        <ul>
                            <li><strong>Prompt_Category:</strong> أكاديمي، تجاري، برمجي، إبداعي، ترجمة، صحي، مالي، إلخ.</li>
                            <li><strong>Agent_Pattern:</strong> Single-Agent، Manager-Agent، Helper-Agent، Decentralized_Handoff.</li>
                        </ul>
                    </li>
                    <li>اقتراح النموذج/الوكيل الأنسب للاستخدام (ChatGPT، Gemini 2.5، Claude 4) بناءً على بيانات التدريب ونتائج نظام المقارنة (مثل: “إذا الفئة ‘صحي’ ولم تُحدد متطلبات أخلاقية عالية، استخدم ChatGPT؛ وإن احتاج الأمر مراقبة أخلاقية، اختر Claude 4_Guardian”).</li>
                </ol>

                <p><strong>بنية النموذج التصنيفي (Classifier Architecture):</strong></p>
                <ul>
                    <li>استخدام نموذج مُسبق التدريب مثل BERT بالعربية أو XLM-R مع طبقة تصنيف نهائية (Dense) لإخراج احتمالات الفئات لكل من Prompt_Category وAgent_Pattern.</li>
                    <li>خطوات جمع بيانات التدريب:
                        <ol>
                            <li>استيراد مئات أمثلة برمبت وتعليمات وكلاء من الخطوة 4 مع تسميات الفئات (Prompt_Category, Agent_Pattern, Recommended_Model).</li>
                            <li>تقسيم البيانات إلى مجموعة تدريب (80%) ومجموعة اختبار (20%).</li>
                            <li>تمثيل النصوص باستخدام Embeddings (BERT) أو استخدام tf-idf إذا كانت البيانات بسيطة.</li>
                        </ol>
                    </li>
                    <li>آلية التقييم والمتابعة: حساب Accuracy، Precision، Recall، F1-score لكل فئة.</li>
                </ul>

                <p><strong>اقتراح خوارزمية التدريب (Pseudocode):</strong></p>
                <pre style="background:#eee; padding:10px; border-radius:4px;">
1. جلب مجموعة البيانات: (Text_Input, Prompt_Category, Agent_Pattern, Recommended_Model).
2. تنظيف النصوص: إزالة الأحرف الخاصة والتشكيل.
3. استخدام BERT لتحويل كل Text_Input إلى Embedding ثابت.
4. بناء طبقات تصنيف:
   - Dense(128, activation='relu')
   - Dropout(0.3)
   - Dense(num_prompt_categories, activation='softmax')  # تصنيف البرمبت
   - Dense(num_agent_patterns, activation='softmax')     # تصنيف نمط الوكيل
5. تدريب النموذج:
   - Loss: فئة البرمبت = CategoricalCrossentropy
   - Loss: نمط الوكيل = CategoricalCrossentropy
   - Optimizer: Adam
   - Batch Size: 32، Epochs: 10
6. تقييم الأداء على مجموعة الاختبار:
   - احسب Accuracy, Precision, Recall, F1-score لكل مهمة.
7. عند التشغيل:
   - أدخل نص BrPrompt/تعليمات الوكيل → احصل على Prompt_Category وAgent_Pattern.
   - استخرج Recommended_Model بناءً على خريطة التوصيات.
                </pre>
            </div>
        </section>

        <!-- Section 10 -->
        <section id="section10" class="collapsed" data-category="examples" data-complexity="intermediate" data-keywords="دليل برمبتات وكلاء احترافي نموذج تجاري أكاديمي برمجي">
            <h2 onclick="toggleSection(this)">
                <span>10. تطوير “دليل برمبتات ووكلاء احترافي” لكل نموذج</span>
                <span class="toggle-icon">▶</span>
            </h2>
            <div class="content">
                <p>في هذا القسم نشرح كيفية تكوين برمبت وتعليمات وكيل موجه لكل نموذج حسب نوع الاستخدام:</p>

                <h3>10.1 للاستخدام التجاري (Business)</h3>
                <ul>
                    <li><strong>متطلبات إضافية:</strong> الحفاظ على السرية (Confidentiality)، تنسيق تقارير متعددة (JSON، CSV، PDF)، تضمين “Flat-Rate Budget Constraint” أو “Token Budget Constraint” ضمن Guardrails.</li>
                    <li><strong>أمثلة برمبت وتعليمات وكيل:</strong></li>
                </ul>
                <table>
                    <thead>
                        <tr>
                            <th>النموذج</th>
                            <th>Prompt</th>
                            <th>تعليمات الوكيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>ChatGPT (Single-Agent)</td>
                            <td>
                                “أنت وكيل دعم تجاري ذكي. أكتب تقريرًا تنفيذيًا عن أداء المبيعات في {quarter} لقطاع {sector}، مع جدول JSON يُبيّن الإيرادات والتكاليف. احرص على عدم تجاوز إجمالي عدد التوكنات 500 توكن. استخدم Chain of Thought لتبرير الاختيارات.”
                            </td>
                            <td>
                                <ul>
                                    <li>Agent_Pattern: Single-Agent</li>
                                    <li>Guardrail_Settings: {Temperature: 0.6, Top_P: 0.85, Safety: true, Token_Limit: 500}</li>
                                    <li>Tool_Use: [“FinancialDB_API”, “JSONFormatter”]</li>
                                    <li>Continual_Learning: False</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Gemini 2.5 (Helper-Agent ضمن Manager-Agent)</td>
                            <td>
                                “أنت وكيل مساعد. قدّم بيانات المبيعات فصل {quarter} من FinancialDB_API بترتيب: (الإيرادات، التكاليف، هامش الربح). كن مختصرًا وواضحًا.”
                            </td>
                            <td>
                                <ul>
                                    <li>Agent_Pattern: Helper-Agent</li>
                                    <li>Guardrail_Settings: {Temperature: 0.5, Top_P: 0.8, Safety: true}</li>
                                    <li>Tool_Use: [“FinancialDB_API”]</li>
                                    <li>Continual_Learning: False</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Claude 4 (Guardian-Agent ضمن Manager-Agent)</td>
                            <td>
                                “بصفتك وكيلًا أخلاقيًا ورسميًا، قُم بمراجعة التقرير الناتج عن ChatGPT_Manager للتحقّق من الالتزام بالسياسات الداخلية للشركة وتقديم ملاحظات حول الجوانب الأخلاقية أو القانونية.”
                            </td>
                            <td>
                                <ul>
                                    <li>Agent_Pattern: Guardian-Agent</li>
                                    <li>Guardrail_Settings: {Temperature: 0.5, Top_P: 0.7, Safety: true, Legal_Check: true}</li>
                                    <li>Tool_Use: [“PolicyChecker_API”]</li>
                                    <li>Continual_Learning: False</li>
                                </ul>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h3>10.2 للأبحاث العلمية (Academic)</h3>
                <ul>
                    <li><strong>متطلبات إضافية:</strong> تضمين استشهادات (Citations) بأسلوب APA-7 أو Vancouver، وضوح الفرضيات والمنهجية، توثيق كل تجربة Prompt Engineering ضمن ملف تتبع.</li>
                    <li><strong>أمثلة برمبت وتعليمات وكيل:</strong></li>
                </ul>
                <table>
                    <thead>
                        <tr>
                            <th>النموذج</th>
                            <th>Prompt</th>
                            <th>تعليمات الوكيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>ChatGPT (Single-Agent مع CoT وSelf-Consistency)</td>
                            <td>
                                “أنت باحث أكاديمي بارع. اكتب ورقة قصيرة (2000–2500 كلمة) حول تأثير {متغير A} على {متغير B} في مجال {domain}، مع تضمين استشهادات APA-7 لكل اقتباس. استخدم Chain of Thought لتفسير المنهجية ثم Self-Consistency لمراجعة الإجابة ثلاث مرات واختيار الأكثر اتساقًا.”
                            </td>
                            <td>
                                <ul>
                                    <li>Agent_Pattern: Single-Agent</li>
                                    <li>Guardrail_Settings: {Temperature: 0.7, Top_P: 0.9, Safety: true, Citation_Format: “APA-7”}</li>
                                    <li>Tool_Use: [“AcademicDB_API”, “CitationFormatter”]</li>
                                    <li>Continual_Learning: True</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Gemini 2.5 (Helper-Agent)</td>
                            <td>
                                “أنت وكيل مساعد بحثي. ابحث في AcademicDB_API عن مقالات حول {متغير A} و{متغير B} بين 2020 و2025، ثم أعد قائمة مكونة من 5 مراجع مناسبة بأسلوب Vancouver.”
                            </td>
                            <td>
                                <ul>
                                    <li>Agent_Pattern: Helper-Agent</li>
                                    <li>Guardrail_Settings: {Temperature: 0.5, Top_P: 0.85, Safety: true}</li>
                                    <li>Tool_Use: [“AcademicDB_API”]</li>
                                    <li>Continual_Learning: False</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Claude 4 (Guardian-Agent)</td>
                            <td>
                                “بصفتك وكيلًا أخلاقيًا أكاديميًا، راجع الفقرات الناتجة عن ChatGPT_Manager للتأكد من عدم وجود الانتحال وتوافق الاستشهادات مع معايير APA-7.”
                            </td>
                            <td>
                                <ul>
                                    <li>Agent_Pattern: Guardian-Agent</li>
                                    <li>Guardrail_Settings: {Temperature: 0.5, Top_P: 0.8, Safety: true, Plagiarism_Check: true}</li>
                                    <li>Tool_Use: [“PlagiarismDetector_API”]</li>
                                    <li>Continual_Learning: False</li>
                                </ul>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h3>10.3 للمهام البرمجية (Programming)</h3>
                <ul>
                    <li><strong>متطلبات إضافية:</strong> طلب مثال كود مفصّل مع تعليقات، اختيار لغة برمجة محدّدة، تضمين “نجدة أخطاء الكود (Debugging)”.</li>
                    <li><strong>أمثلة برمبت وتعليمات وكيل:</strong></li>
                </ul>
                <table>
                    <thead>
                        <tr>
                            <th>النموذج</th>
                            <th>Prompt</th>
                            <th>تعليمات الوكيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>ChatGPT (Single-Agent مع ReAct)</td>
                            <td>
                                “أنت مهندس برمجيات متقدّم. اكتب كودًا بلغة Python يستخدم ReAct للبحث عن بيانات من OpenWeather API، ثم يعرض الخطوات المنطقية (CoT) لكيفية التعامل مع الأخطاء المحتملة.”
                            </td>
                            <td>
                                <ul>
                                    <li>Agent_Pattern: Single-Agent</li>
                                    <li>Guardrail_Settings: {Temperature: 0.6, Top_P: 0.9, Safety: true, Debug_Mode: true}</li>
                                    <li>Tool_Use: [“OpenWeather_API”, “Logger”]</li>
                                    <li>Continual_Learning: False</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Gemini 2.5 (Helper-Agent)</td>
                            <td>
                                “أنت وكيل مساعد برمجي. استدعي OpenWeather_API للحصول على بيانات الطقس الحالية لصنف {city} وأعد JSON يتضمن الحرارة والرطوبة.”
                            </td>
                            <td>
                                <ul>
                                    <li>Agent_Pattern: Helper-Agent</li>
                                    <li>Guardrail_Settings: {Temperature: 0.5, Top_P: 0.8, Safety: true}</li>
                                    <li>Tool_Use: [“OpenWeather_API”]</li>
                                    <li>Continual_Learning: False</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Claude 4 (Guardian-Agent)</td>
                            <td>
                                “بصفتك وكيل مراجعة البرمجيات، راجع الكود الناتج من ChatGPT_Manager للتحقق من وجود ثغرات أمنية أو أخطاء منطقية وأعط اقتراحات للتحسين.”
                            </td>
                            <td>
                                <ul>
                                    <li>Agent_Pattern: Guardian-Agent</li>
                                    <li>Guardrail_Settings: {Temperature: 0.5, Top_P: 0.85, Safety: true, Security_Check: true}</li>
                                    <li>Tool_Use: [“SecurityAnalyzer_API”]</li>
                                    <li>Continual_Learning: False</li>
                                </ul>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h3>10.4 للإبداع والكتابة الأدبية (Creative Writing)</h3>
                <ul>
                    <li><strong>متطلبات إضافية:</strong> تحفيز النموذج لتوليد سرد قصصي أو شعر مع التحكم في طول الفقرة وأسلوب السرد، تضمين عناصر Tree of Thought، وضع ضوابط أخلاقية لتجنب المحتوى المرفوض.</li>
                    <li><strong>أمثلة برمبت وتعليمات وكيل:</strong></li>
                </ul>
                <table>
                    <thead>
                        <tr>
                            <th>النموذج</th>
                            <th>Prompt</th>
                            <th>تعليمات الوكيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>ChatGPT (Single-Agent مع ToT)</td>
                            <td>
                                “اكتب قصة خيالية قصيرة (600–800 كلمة) تدور أحداثها في مدينةٍ عائمة على سطح الماء، مستخدمًا Tree of Thought لاستعراض مسارات سردية مختلفة (على الأقل 3 فروع)، واحرص على ألا تتضمن القصة محتوى محظور أو غير لائق.”
                            </td>
                            <td>
                                <ul>
                                    <li>Agent_Pattern: Single-Agent</li>
                                    <li>Guardrail_Settings: {Temperature: 0.8, Top_P: 0.95, Safety: true, Content_Filter: “PG-13”}</li>
                                    <li>Tool_Use: [“StoryOutlineGenerator”]</li>
                                    <li>Continual_Learning: False</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Gemini 2.5 (Helper-Agent)</td>
                            <td>
                                “أنت وكيل مساعد إبداعي. اقترح 3 نقاط انطلاق سردية لقصة في مدينةٍ عائمة على الماء، بجمل مختصرة لا تتجاوز 50 كلمة لكل نقطة.”
                            </td>
                            <td>
                                <ul>
                                    <li>Agent_Pattern: Helper-Agent</li>
                                    <li>Guardrail_Settings: {Temperature: 0.7, Top_P: 0.9, Safety: true}</li>
                                    <li>Tool_Use: []</li>
                                    <li>Continual_Learning: False</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td>Claude 4 (Guardian-Agent)</td>
                            <td>
                                “بصفتك وكيلًا أخلاقيًا أدبيًا، راجع عناصر القصة لتتأكد من عدم تضمين أي محتوى ينتهك المعايير الثقافية أو الأخلاقية.”
                            </td>
                            <td>
                                <ul>
                                    <li>Agent_Pattern: Guardian-Agent</li>
                                    <li>Guardrail_Settings: {Temperature: 0.5, Top_P: 0.8, Safety: true, Cultural_Check: true}</li>
                                    <li>Tool_Use: []</li>
                                    <li>Continual_Learning: False</li>
                                </ul>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h3>نصائح عملية (Tips & Tricks) لكل نموذج/وكيل:</h3>
                <ul>
                    <li><strong>ChatGPT:</strong> استخدم CoT وSelf-Consistency لتحسين الدقة، وضع حماية ضد تجاوز Token Budget Limit إذا كانت التكلفة معيارًا أساسيًا.</li>
                    <li><strong>Gemini 2.5:</strong> اجعل التعليمات بسيطة ومباشرة للحصول على استجابات سريعة، خصصه كـ Helper-Agent لعمليات استرجاع أو حوسبة بسيطة ضمن بنية Multi-Agent.</li>
                    <li><strong>Claude 4:</strong> استخدمه كـ Guardian-Agent للتحقق الأخلاقي والثقافي، ضمن التوجيه: “راجع الأخلاقيات أولًا” أو “امنع أي محتوى مسيء.”</li>
                </ul>

                <h3>الأخطاء الشائعة (Common Pitfalls) وحلولها:</h3>
                <table>
                    <thead>
                        <tr>
                            <th>الخطأ</th>
                            <th>الوصف</th>
                            <th>طريقة التجنب</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>برمبت عام جدًا</td>
                            <td>لا يحدد الهدف أو السياق بوضوح، مما يؤدي إلى خروج النموذج عن الموضوع</td>
                            <td>استخدم System/Role/Context Prompts واضحة وتضمّن حالات الدخول والخروج لـ Agent Triggers وExit Conditions.</td>
                        </tr>
                        <tr>
                            <td>كثرة الطبقات دون ضرورة</td>
                            <td>استخدام CoT وToT بشكل زائد قد يزيد من زمن الاستجابة</td>
                            <td>حدد متى تستخدم CoT أو ToT فقط في الخطوات الحرجة، وقللها في المهام البسيطة.</td>
                        </tr>
                        <tr>
                            <td>إهمال الضوابط (Guardrails)</td>
                            <td>عدم تضمين مصنّفات الأمان والقيم الأخلاقية، مما يتيح مخرجات قد تكون ضارة</td>
                            <td>أضف دائمًا خطوط الأمان (Safety Classifier, PII Filter, Content Filter) لكل برمبت أو تعليمات وكيل.</td>
                        </tr>
                        <tr>
                            <td>عدم تحديث الوكيل (Static Agent)</td>
                            <td>لا وجود آلية لتحديث المعرفة عند ظهور معلومات جديدة، مما يقلل دقة الردود بمرور الوقت</td>
                            <td>استخدم تكامل Continual Learning عبر تحديث System Prompts أو إجراء Fine-Tuning دوري.</td>
                        </tr>
                        <tr>
                            <td>تجاهل التقييم الأوتوماتيكي</td>
                            <td>الاعتماد فقط على التقييم البشري ما يبطئ دورة التطوير</td>
                            <td>طبق Meta-Evaluation باستخدام نموذج ثانٍ لتقييم (Accuracy, Creativity, Clarity, Efficiency, Compliance).</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Section 11 -->
        <section id="section11" class="collapsed" data-category="tools" data-complexity="advanced" data-keywords="Dashboard تفاعلي مراقبة أداء وكلاء برمبتات تحليلات">
            <h2 onclick="toggleSection(this)">
                <span>11. إنشاء Dashboard تفاعلي لمراقبة أداء الوكلاء والبرمبتات</span>
                <span class="toggle-icon">▶</span>
            </h2>
            <div class="content">
                <p>وصف كامل لإنشاء Dashboard تفاعلي باستخدام Streamlit أو Gradio:</p>

                <h3>11.1 قسم التحميل (Upload Section)</h3>
                <ul>
                    <li>واجهة لرفع ملف CSV أو Excel يحتوي أعمدة:
                        <ul>
                            <li><strong>Prompt_Text:</strong> نص البرمبت أو تعليمات الوكيل.</li>
                            <li><strong>Agent_Pattern:</strong> نمط الوكيل (Single-Agent, Helper-Agent، إلخ).</li>
                            <li><strong>Guardrail_Settings:</strong> JSON يحتوي إعدادات Guardrails (مثال: <code>{"Temperature": 0.7, "Top_P": 0.9, "Safety": true}</code>).</li>
                        </ul>
                    </li>
                    <li>استخدام <code>st.file_uploader("اختر ملف CSV أو Excel")</code>.</li>
                </ul>

                <h3>11.2 قسم إرسال البرمبتات/الوكلاء (Prompt/Agent Dispatch)</h3>
                <ul>
                    <li>زر “إرسال جميع البرمبتات للوكلاء” يرسل كل برمبت إلى الوكيل المناسب عبر API.</li>
                    <li>منطق التوزيع:
                        <ul>
                            <li>إذا <em>Agent_Pattern</em> = “Single-Agent”، استدعِ ChatGPT مباشرةً.</li>
                            <li>إذا = “Manager-Agent”، استدعِ وكيل المدير (ChatGPT_Manager) ليَنسّق Helper-Agents (Gemini 2.5_Helper).</li>
                            <li>إذا = “Guardian-Agent”، استدعِ Claude 4 للتحقق الأخلاقي.</li>
                        </ul>
                    </li>
                    <li>مثال كود Streamlit مبسط في القسم السابق (انظر الخطوة 11.2 في الوثيقة الأصلية).</li>
                </ul>

                <h3>11.3 قسم عرض النتائج (Results Display)</h3>
                <ul>
                    <li>عرض جدول يحتوي:
                        <ul>
                            <li>Prompt_ID</li>
                            <li>Prompt_Text</li>
                            <li>Agent_Pattern</li>
                            <li>Guardrail_Settings</li>
                            <li>Response_Text</li>
                            <li>Ratings_Accuracy</li>
                            <li>Ratings_Creativity</li>
                            <li>Ratings_Clarity</li>
                            <li>Ratings_Efficiency</li>
                            <li>Ratings_Compliance</li>
                            <li>Ratings_EthicalAlignment</li>
                            <li>AgentLatency_ms</li>
                            <li>MultiAgentCoordinationScore (إن وجد)</li>
                        </ul>
                    </li>
                    <li>استخدام <code>st.dataframe(df_results)</code> أو <code>st.table(df_results)</code>.</li>
                </ul>

                <h3>11.4 قسم المقارنة المرئية (Visual Comparison)</h3>
                <ul>
                    <li>Bar Charts لمقارنة متوسط الدرجات لكل معيار عبر كل Agent_Pattern:
                        <pre style="background:#eef; padding:10px; border-radius:4px;">
avg_scores = df_results.groupby("Agent_Pattern").mean()[[
    "Ratings_Accuracy",
    "Ratings_Creativity",
    "Ratings_Clarity",
    "Ratings_Efficiency",
    "Ratings_Compliance",
    "Ratings_EthicalAlignment"
]]
st.write("### المتوسطات لكل نمط وكيل")
st.bar_chart(avg_scores)
                        </pre>
                    </li>
                    <li>Radar Charts لإظهار توزيع الدرجات لكل نموذج/وكيل (يمكن استخدام مكتبة matplotlib داخل Streamlit).</li>
                    <li>Line Chart يوضح تغير الأداء عبر إصدارات الوكيل (Version).</li>
                </ul>

                <h3>11.5 قسم التوصيات (Recommendations)</h3>
                <ul>
                    <li>توليد توصيات تلقائية بناءً على النتائج والمقارنة (اختيار الأفضل حسب أعلى الدرجات):
                        <pre style="background:#eef; padding:10px; border-radius:4px;">
recommendations = []
for idx, row in df_results.iterrows():
    best_agent = max([
        ("ChatGPT_SingleAgent", row["Ratings_Accuracy"]),
        ("Gemini2.5_Helper", row["Ratings_Accuracy_Gemini_Helper"]) if "Ratings_Accuracy_Gemini_Helper" in row else (None, -1),
        ("Claude4_Guardian", row["Ratings_Accuracy_Claude_Guardian"]) if "Ratings_Accuracy_Claude_Guardian" in row else (None, -1)
    ], key=lambda x: x[1])[0]
    recommendations.append(f"أفضل وكيل للبرمبت رقم {idx+1} هو {best_agent} بناءً على أعلى درجة دقة وامتثال أخلاقي.")
st.write("### التوصيات النهائية")
st.write("\n".join(recommendations))
                        </pre>
                    </li>
                </ul>

                <h3>11.6 قائمة التنقل (Navigation Menu)</h3>
                <ul>
                    <li>إضافة شريط جانبي للتنقل بين أقسام Dashboard:
                        <pre style="background:#eef; padding:10px; border-radius:4px;">
choice = st.sidebar.radio("القائمة الرئيسية", ["تحميل","إرسال","عرض النتائج","المقارنة","التوصيات"])
if choice == "تحميل":
    # قسم التحميل
elif choice == "إرسال":
    # قسم الإرسال
elif choice == "عرض النتائج":
    # عرض الجدول
elif choice == "المقارنة":
    # عرض المخططات
elif choice == "التوصيات":
    # عرض التوصيات
                        </pre>
                    </li>
                </ul>
            </div>
        </section>

        <!-- Section 12 -->
        <section id="section12" class="collapsed" data-category="summary" data-complexity="intermediate" data-keywords="ملخص خطوات تخصيص متغيرات نهائي خلاصة">
            <h2 onclick="toggleSection(this)">
                <span>12. ملخص خطوات التخصيص والمتغيرات</span>
                <span class="toggle-icon">▶</span>
            </h2>
            <div class="content">
                <p>جدول المتغيرات وكيفية استخدامها داخل البرمبت/التعليمات مع أمثلة:</p>
                <table>
                    <thead>
                        <tr>
                            <th>المتغير</th>
                            <th>الوصف</th>
                            <th>نموذج الاستخدام في البرمبت/التعليمات</th>
                            <th>مثال للقيمة القابلة للتخصيص</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>{مجال البحث أو التطبيق المستهدف}</code></td>
                            <td>تحديد مجال الدراسة أو التطبيق العملي</td>
                            <td>“تهدف الدراسة إلى تطوير وكلاء في مجال <code>{القطاع_الصحي}</code>…”</td>
                            <td><code>{القطاع_الصحي}</code>, <code>{التعليم_عن_بعد}</code></td>
                        </tr>
                        <tr>
                            <td><code>{النماذج الثلاثة}</code></td>
                            <td>النماذج المراد مقارنتها</td>
                            <td>“مقارنة سلوك كلٍّ من <code>{ChatGPT gpt-4o-mini, Gemini 2.5, Claude 4}</code>…”</td>
                            <td><code>{ChatGPT gpt-4o-mini, Gemini 2.5, Claude 4}</code></td>
                        </tr>
                        <tr>
                            <td><code>{الوكيل المطلوب بناؤه أو استخدامه}</code></td>
                            <td>نمط الوكيل (Single-Agent أو Manager-Agent…)</td>
                            <td>“اصنع وكيلًا من نوع <code>{Manager-Agent}</code> ينسق بين Helper-Agents…”</td>
                            <td><code>{Single-Agent}</code>, <code>{Multi-Agent}</code>, <code>{Helper-Agent}</code>, <code>{Guardian-Agent}</code></td>
                        </tr>
                        <tr>
                            <td><code>{النتيجة المستهدفة من الدراسة}</code></td>
                            <td>الهدف المفصّل المرجو تحقيقه</td>
                            <td>“الهدف النهائي هو <code>{تحسين_دقة_الوكلاء_الصحية}</code>.”</td>
                            <td><code>{رفع_كفاءة_دعم_العملاء}</code>, <code>{أتمتة_الاختبارات_التعليميية}</code></td>
                        </tr>
                        <tr>
                            <td><code>{مصادر الأمثلة}</code></td>
                            <td>المصادر التي يُستدل منها على أمثلة البرمبت ووكلاء</td>
                            <td>“جمع الأمثلة من <code>{Reddit_r_PromptEngineering, GitHub_AI-Agent-Playbook}</code>.”</td>
                            <td><code>{Reddit_r_PromptEngineering}</code>, <code>{GitHub_AI-Agent-Playbook}</code>, <code>{PapersWithCode}</code></td>
                        </tr>
                        <tr>
                            <td><code>{نوع الاستخدام}</code></td>
                            <td>السياق العام للاستعمال (تجاري، أكاديمي، إلخ.)</td>
                            <td>“ستُستخدم هذه الوكلاء في <code>{أكاديمي}</code> لأغراض بحثية.”</td>
                            <td><code>{تجاري}</code>, <code>{أكاديمي}</code>, <code>{برمجي}</code>, <code>{صحي}</code>, <code>{مالي}</code></td>
                        </tr>
                        <tr>
                            <td><code>{نمط البرمبتات والوكيل المرغوب دراسته}</code></td>
                            <td>أسلوب البرمبت أو نمط الوكيل (Zero-Shot, CoT, ToT, ReAct)</td>
                            <td>“اختبر كل الأساليب التالية: <code>{Chain_of_Thought, Tree_of_Thought, ReAct}</code>.”</td>
                            <td><code>{Zero-Shot}</code>, <code>{Few-Shot}</code>, <code>{Chain_of_Thought}</code>, <code>{Tree_of_Thought}</code>, <code>{ReAct}</code></td>
                        </tr>
                        <tr>
                            <td><code>{Shared Guardrail_Settings}</code></td>
                            <td>إعدادات Guardrails مشتركة (مثل Temperature, Top_P, Safety, PII_Filter)</td>
                            <td>“Guardrails: <code>{'Temperature':0.7, 'Top_P':0.9, 'Safety':True, 'PII_Filter':True}</code>.”</td>
                            <td><code>{Temperature:0.7}</code>, <code>{Top_P:0.9}</code>, <code>{Safety:True}</code>, <code>{PII_Filter:True}</code></td>
                        </tr>
                        <tr>
                            <td><code>{Agent_Pattern}</code></td>
                            <td>نمط الوكيل (Single-Agent, Manager-Agent, Helper-Agent, Decentralized_Handoff)</td>
                            <td>“Agent_Pattern: <code>{Manager-Agent}</code> لتنفيذ تنسيق المهام.”</td>
                            <td><code>{Single-Agent}</code>, <code>{Manager-Agent}</code>, <code>{Helper-Agent}</code>, <code>{Decentralized_Handoff}</code></td>
                        </tr>
                        <tr>
                            <td><code>{Tool_Use}</code></td>
                            <td>قائمة الأدوات أو واجهات API التي يستدعيها الوكيل</td>
                            <td>“Tool_Use: <code>['HealthcareDB_API','MedicationInfo_API']</code>.”</td>
                            <td><code>{["FinancialDB_API","JSONFormatter"]}</code>, <code>{["AcademicDB_API","CitationFormatter"]}</code></td>
                        </tr>
                        <tr>
                            <td><code>{Continual_Learning_Flag}</code></td>
                            <td>مؤشر يدعم التعلم المستمر (True/False)</td>
                            <td>“Continual_Learning: <code>{True}</code> لتحديث المعرفة تلقائيًا.”</td>
                            <td><code>{True}</code>, <code>{False}</code></td>
                        </tr>
                        <tr>
                            <td><code>{Citation_Format}</code></td>
                            <td>صيغة الاستشهادات للبحث الأكاديمي (APA-7 أو Vancouver)</td>
                            <td>“استخدم <code>{Citation_Format:'APA-7'}</code> في كل استشهاد.”</td>
                            <td><code>{'APA-7'}</code>, <code>{'Vancouver'}</code></td>
                        </tr>
                        <tr>
                            <td><code>{Token_Budget_Limit}</code></td>
                            <td>الحد الأقصى لعدد التوكنات المستخدم في كل استدعاء</td>
                            <td>“Token_Budget_Limit: <code>{500}</code>.”</td>
                            <td><code>{500}</code>, <code>{1024}</code>, <code>{2048}</code></td>
                        </tr>
                        <tr>
                            <td><code>{Evaluation_Criteria}</code></td>
                            <td>قائمة معايير التقييم (Accuracy, Creativity, Clarity, Efficiency, Compliance, EthicalAlignment)</td>
                            <td>“Evaluation_Criteria: <code>['Accuracy','Creativity','Clarity','Efficiency','Compliance','EthicalAlignment']</code>.”</td>
                            <td><code>['Accuracy','Creativity','Clarity','Efficiency','Compliance','EthicalAlignment']</code></td>
                        </tr>
                        <tr>
                            <td><code>{Database_Fields}</code></td>
                            <td>أسماء حقول جدول قاعدة البيانات المصنّفة</td>
                            <td>“Database_Fields: <code>{Prompt_ID, Prompt_Text, Prompt_Type, Agent_Pattern, Domain, Complexity_Level, Source, Initial_Performance_Notes, Guardrail_Settings, Continual_Learning_Flag}</code>.”</td>
                            <td><code>{Prompt_ID, Prompt_Text, Prompt_Type, Agent_Pattern, Domain, Complexity_Level, Source, Initial_Performance_Notes, Guardrail_Settings, Continual_Learning_Flag}</code></td>
                        </tr>
                        <tr>
                            <td><code>{Version}</code></td>
                            <td>رقم إصدار الإطار أو الوكيل</td>
                            <td>“Version: <code>{1.0}</code> للإشارة إلى الإصدار الأولي.”</td>
                            <td><code>{1.0}</code>, <code>{2.1}</code>, <code>{0.9}</code></td>
                        </tr>
                    </tbody>
                </table>

                <p><em>ملاحظة:</em> عند استخدام هذا البرمبت الشامل، استبدل كل متغير موضوع بين <code>{}</code> بالقيمة المناسبة لديك قبل التنفيذ. يعمل هذا الإطار البحثي المُطوّر كنقطة انطلاق لأي مشروع يهدف إلى دمج تقنيات هندسة البرمبت (Prompt Engineering) مع تصميم وبناء وكلاء ذكيين (Agent Design)، ممّا يمكّنك من إنشاء نظم قادرة على التعامل مع المهام المعقدة بكفاءة عالية، وضمان الالتزام الأخلاقي وتنفيذ استراتيجيات التعلم المستمر.</p>
            </div>
        </section>

    </div>

    <!-- زر العودة للأعلى -->
    <button id="backToTop" aria-label="العودة للأعلى">↑</button>

    <script>
        // التعامل مع تحكم القائمة في الأجهزة الصغيرة
        const mobileMenu = document.getElementById('mobile-menu');
        const navLinks = document.getElementById('nav-links');
        mobileMenu.addEventListener('click', () => {
            navLinks.classList.toggle('show');
            mobileMenu.classList.toggle('open');
        });

        // وظائف طي/فتح الأقسام مع حفظ الحالة في localStorage
        function toggleSectionOld(header) {
            const section = header.closest('section');
            const content = section.querySelector('.content');
            const icon = header.querySelector('.toggle-icon');
            const sectionId = section.id;

            if (content.style.display === 'block') {
                content.style.display = 'none';
                section.classList.add('collapsed');
                icon.textContent = '▶';
                localStorage.setItem(sectionId, 'collapsed');
            } else {
                content.style.display = 'block';
                section.classList.remove('collapsed');
                icon.textContent = '▼';
                localStorage.setItem(sectionId, 'expanded');
            }
        }

        // وظيفة toggleSection الجديدة التي تستخدم sectionManager
        function toggleSection(header) {
            // منع التنفيذ المتعدد
            if (header.dataset.processing === 'true') return;
            header.dataset.processing = 'true';

            setTimeout(() => {
                header.dataset.processing = 'false';
            }, 100);

            // التحقق من وجود sectionManager
            if (window.sectionManager && window.sectionManager.toggle) {
                window.sectionManager.toggle(header);
            } else {
                // استخدام الوظيفة القديمة كبديل
                toggleSectionOld(header);
            }
        }

        // استعادة حالة الأقسام عند تحميل الصفحة (سيتم التعامل معها في app.js)
        document.addEventListener('DOMContentLoaded', () => {
            highlightActiveLink(); // تمييز الرابط النشط عند التحميل
        });

        // تمييز الرابط النشط بناءً على قسم في العرض
        const navAnchors = document.querySelectorAll('nav ul li a');
        const sections = document.querySelectorAll('section');

        function highlightActiveLink() {
            let scrollPos = window.scrollY + 100; // إزاحة بسيطة للتأكد من الطريق
            sections.forEach(sec => {
                if (scrollPos >= sec.offsetTop && scrollPos < sec.offsetTop + sec.offsetHeight) {
                    const id = sec.id;
                    navAnchors.forEach(link => {
                        link.classList.remove('active');
                        if (link.getAttribute('href') === `#${id}`) {
                            link.classList.add('active');
                        }
                    });
                }
            });
        }
        window.addEventListener('scroll', () => {
            highlightActiveLink();

            // إظهار زر “العودة للأعلى”
            const backBtn = document.getElementById('backToTop');
            if (window.scrollY > 300) {
                backBtn.style.display = 'block';
            } else {
                backBtn.style.display = 'none';
            }
        });

        // وظيفة العودة للأعلى
        document.getElementById('backToTop').addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        // وظائف البحث والفلترة
        const searchBox = document.getElementById('searchBox');
        const filterCategory = document.getElementById('filterCategory');
        const filterComplexity = document.getElementById('filterComplexity');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const searchableSections = document.querySelectorAll('section[data-category]');

        // وظيفة البحث
        function performSearch() {
            const searchTerm = searchBox.value.toLowerCase().trim();
            const selectedCategory = filterCategory.value;
            const selectedComplexity = filterComplexity.value;

            // إظهار مؤشر التحميل
            loadingIndicator.style.display = 'block';

            setTimeout(() => {
                searchableSections.forEach(section => {
                    const keywords = section.getAttribute('data-keywords').toLowerCase();
                    const category = section.getAttribute('data-category');
                    const complexity = section.getAttribute('data-complexity');
                    const sectionText = section.textContent.toLowerCase();

                    let showSection = true;

                    // فلترة البحث النصي
                    if (searchTerm && !keywords.includes(searchTerm) && !sectionText.includes(searchTerm)) {
                        showSection = false;
                    }

                    // فلترة الفئة
                    if (selectedCategory !== 'all' && category !== selectedCategory) {
                        showSection = false;
                    }

                    // فلترة مستوى التعقيد
                    if (selectedComplexity !== 'all' && complexity !== selectedComplexity) {
                        showSection = false;
                    }

                    // إظهار أو إخفاء القسم
                    if (showSection) {
                        section.style.display = 'block';
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    } else {
                        section.style.display = 'none';
                    }
                });

                // إخفاء مؤشر التحميل
                loadingIndicator.style.display = 'none';
            }, 300);
        }

        // ربط أحداث البحث والفلترة
        searchBox.addEventListener('input', performSearch);
        filterCategory.addEventListener('change', performSearch);
        filterComplexity.addEventListener('change', performSearch);

        // وظيفة تمييز النص المطابق
        function highlightSearchTerm(text, term) {
            if (!term) return text;
            const regex = new RegExp(`(${term})`, 'gi');
            return text.replace(regex, '<mark>$1</mark>');
        }

        // تحسين تجربة المستخدم - إضافة تأثيرات انتقالية
        searchableSections.forEach(section => {
            section.style.transition = 'all 0.3s ease';
        });

        // وظيفة إعادة تعيين البحث
        function resetSearch() {
            searchBox.value = '';
            filterCategory.value = 'all';
            filterComplexity.value = 'all';
            searchableSections.forEach(section => {
                section.style.display = 'block';
                section.style.opacity = '1';
                section.style.transform = 'translateY(0)';
            });
        }

        // إضافة زر إعادة تعيين (يمكن إضافته في HTML لاحقاً)
        const resetButton = document.createElement('button');
        resetButton.textContent = 'إعادة تعيين';
        resetButton.className = 'filter-select';
        resetButton.style.backgroundColor = '#f44336';
        resetButton.style.color = 'white';
        resetButton.addEventListener('click', resetSearch);
        document.querySelector('.search-filter-container').appendChild(resetButton);

        // تحسين الأداء - debounce للبحث
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // استبدال البحث المباشر بـ debounced search
        const debouncedSearch = debounce(performSearch, 300);
        searchBox.removeEventListener('input', performSearch);
        searchBox.addEventListener('input', debouncedSearch);

        // إحصائيات البحث
        function updateSearchStats() {
            const visibleSections = Array.from(searchableSections).filter(section =>
                section.style.display !== 'none'
            ).length;

            const totalSections = searchableSections.length;

            // يمكن إضافة عنصر لعرض الإحصائيات
            console.log(`عرض ${visibleSections} من أصل ${totalSections} قسم`);
        }

        // تحديث الإحصائيات بعد كل بحث
        const originalPerformSearch = performSearch;
        performSearch = function() {
            originalPerformSearch();
            setTimeout(updateSearchStats, 350);
        };
    </script>

    <!-- ربط ملفات JavaScript المطورة بالترتيب الصحيح -->
    <script src="app.js"></script>
    <script>
        // التأكد من تحميل app.js أولاً
        console.log('✅ تم تحميل app.js');

        // تصدير toggleSection للاستخدام العام
        if (window.sectionManager) {
            window.toggleSection = (headerElement) => {
                console.log('🔄 استدعاء toggleSection:', headerElement);
                return window.sectionManager.toggle(headerElement);
            };
        }
    </script>
    <script src="smart-search.js"></script>
    <script src="ai-assistant.js"></script>
    <script src="rating-system.js"></script>
    <script src="analytics-dashboard.js"></script>

    <!-- تهيئة التكامل بين المكونات -->
    <script>
        // ربط البحث الذكي مع نظام التحليلات
        if (window.smartSearch && window.analyticsDashboard) {
            const originalPerformSearch = window.smartSearch.performSearch;
            window.smartSearch.performSearch = async function(query) {
                const result = await originalPerformSearch.call(this, query);
                // تسجيل البحث في التحليلات
                window.analyticsDashboard.logSearchQuery(query, result?.length || 0);
                return result;
            };
        }

        // ربط نظام التقييم مع التحليلات
        if (window.ratingSystem && window.analyticsDashboard) {
            const originalSaveUserRating = window.ratingSystem.saveUserRating;
            window.ratingSystem.saveUserRating = function(sectionId, rating) {
                originalSaveUserRating.call(this, sectionId, rating);
                // تسجيل التقييم في التحليلات
                window.analyticsDashboard.trackInteraction('rating', {
                    sectionId,
                    rating,
                    timestamp: new Date().toISOString()
                });
            };
        }

        // إضافة اختصارات لوحة المفاتيح المتقدمة
        document.addEventListener('keydown', (e) => {
            // Alt + A لفتح المساعد الذكي
            if (e.altKey && e.key === 'a') {
                e.preventDefault();
                if (window.aiAssistant) {
                    window.aiAssistant.toggleAssistant();
                }
            }

            // Alt + D لفتح لوحة التحكم
            if (e.altKey && e.key === 'd') {
                e.preventDefault();
                if (window.analyticsDashboard) {
                    window.analyticsDashboard.toggleDashboard();
                }
            }

            // Alt + R لإعادة تعيين جميع الفلاتر
            if (e.altKey && e.key === 'r') {
                e.preventDefault();
                if (window.smartSearch) {
                    window.smartSearch.resetSearch();
                }
                if (window.searchManager) {
                    window.searchManager.reset();
                }
            }
        });

        // إظهار رسالة ترحيب للمستخدمين الجدد
        window.addEventListener('load', () => {
            const isFirstVisit = !localStorage.getItem('hasVisited');
            if (isFirstVisit) {
                localStorage.setItem('hasVisited', 'true');

                setTimeout(() => {
                    if (window.enhancedNavManager) {
                        window.enhancedNavManager.showNotification(
                            '🎉 مرحباً بك! الموقع محسّن بالكامل مع جدول محتويات تفاعلي ومتتبع ذكي',
                            'info'
                        );

                        // إظهار الاختصارات الجديدة
                        setTimeout(() => {
                            window.enhancedNavManager.showNotification(
                                '⌨️ اختصارات جديدة: Ctrl+T (جدول المحتويات)، Ctrl+M (المتتبع)، Ctrl+← → (التنقل)',
                                'info'
                            );
                        }, 4000);
                    }
                }, 3000);
            }
        });

        // التحقق النهائي من جميع المكونات
        console.log('🚀 تم تحميل جميع المكونات المتقدمة بنجاح!');
    </script>

    <!-- الإصلاح النهائي الشامل -->
    <script src="final-fix.js"></script>

    <!-- نظام الأقسام المحسّن مع التتبع -->
    <script src="enhanced-sections.js"></script>

    <!-- تكامل الأنظمة -->
    <script src="sections-integration.js"></script>

    <script>
        console.log('📋 الاختصارات المتاحة:');
        console.log('   Ctrl+K: البحث السريع');
        console.log('   Alt+A: المساعد الذكي');
        console.log('   Alt+D: لوحة التحكم');
        console.log('   Alt+R: إعادة تعيين الفلاتر');
        console.log('   Escape: إغلاق النوافذ');

        // تقرير حالة المكونات
        console.log('📊 حالة المكونات:');
        console.log('   sectionManager:', window.sectionManager ? '✅' : '❌');
        console.log('   smartSearch:', window.smartSearch ? '✅' : '❌');
        console.log('   aiAssistant:', window.aiAssistant ? '✅' : '❌');
        console.log('   ratingSystem:', window.ratingSystem ? '✅' : '❌');
        console.log('   analyticsDashboard:', window.analyticsDashboard ? '✅' : '❌');
        console.log('   toggleSection:', typeof window.toggleSection === 'function' ? '✅' : '❌');

        // اختبار سريع لوظيفة toggleSection
        if (typeof window.toggleSection === 'function') {
            console.log('✅ وظيفة toggleSection جاهزة للاستخدام');
        } else {
            console.warn('⚠️ وظيفة toggleSection غير متاحة - سيتم إنشاء وظيفة بديلة');

            // إنشاء وظيفة بديلة
            window.toggleSection = function(header) {
                console.log('🔄 استخدام الوظيفة البديلة لـ toggleSection');
                const section = header.closest('section');
                const content = section.querySelector('.content');
                const icon = header.querySelector('.toggle-icon');

                if (content && icon) {
                    const isCollapsed = section.classList.contains('collapsed') || content.style.display === 'none';

                    if (isCollapsed) {
                        section.classList.remove('collapsed');
                        content.style.display = 'block';
                        icon.textContent = '▼';
                    } else {
                        section.classList.add('collapsed');
                        content.style.display = 'none';
                        icon.textContent = '▶';
                    }
                }
            };
        }
    </script>
</body>
</html>
