// مساعد ذكي تفاعلي للموقع
class AIAssistant {
    constructor() {
        this.isOpen = false;
        this.conversationHistory = [];
        this.knowledgeBase = new Map();
        this.isTyping = false;
        
        this.init();
    }

    async init() {
        await this.buildKnowledgeBase();
        this.createAssistantUI();
        this.setupEventListeners();
        console.log('🤖 المساعد الذكي جاهز');
    }

    // بناء قاعدة المعرفة من محتوى الموقع
    async buildKnowledgeBase() {
        const sections = document.querySelectorAll('section[data-category]');
        
        sections.forEach(section => {
            const title = section.querySelector('h2')?.textContent || '';
            const content = section.querySelector('.content')?.textContent || '';
            const category = section.getAttribute('data-category');
            const keywords = section.getAttribute('data-keywords')?.split(' ') || [];
            
            this.knowledgeBase.set(section.id, {
                title,
                content,
                category,
                keywords,
                summary: this.generateSummary(content)
            });
        });
    }

    // توليد ملخص للمحتوى
    generateSummary(content) {
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
        return sentences.slice(0, 3).join('. ') + '.';
    }

    // إنشاء واجهة المساعد
    createAssistantUI() {
        const assistantHTML = `
            <div id="ai-assistant" class="ai-assistant">
                <div class="assistant-header">
                    <div class="assistant-avatar">🤖</div>
                    <div class="assistant-info">
                        <h3>مساعد البرمبت الذكي</h3>
                        <span class="assistant-status">متصل</span>
                    </div>
                    <button class="assistant-close" id="closeAssistant">✕</button>
                </div>
                
                <div class="assistant-chat" id="assistantChat">
                    <div class="welcome-message">
                        <div class="message assistant-message">
                            <div class="message-avatar">🤖</div>
                            <div class="message-content">
                                <p>مرحباً! أنا مساعدك الذكي في هندسة البرمبت. يمكنني مساعدتك في:</p>
                                <ul>
                                    <li>🔍 البحث عن معلومات محددة</li>
                                    <li>💡 اقتراح برمبتات مناسبة</li>
                                    <li>📊 مقارنة النماذج المختلفة</li>
                                    <li>🎯 تحسين البرمبتات الموجودة</li>
                                </ul>
                                <p>اسألني أي سؤال!</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="assistant-input">
                    <div class="quick-questions">
                        <button class="quick-btn" data-question="ما هي أفضل طريقة لكتابة برمبت فعال؟">
                            💡 برمبت فعال
                        </button>
                        <button class="quick-btn" data-question="كيف أقارن بين ChatGPT وClaude؟">
                            ⚖️ مقارنة النماذج
                        </button>
                        <button class="quick-btn" data-question="ما هي أنواع الوكلاء الذكيين؟">
                            🤖 أنواع الوكلاء
                        </button>
                    </div>
                    <div class="input-container">
                        <input type="text" id="assistantInput" placeholder="اكتب سؤالك هنا..." />
                        <button id="sendMessage">📤</button>
                    </div>
                </div>
            </div>
            

        `;

        document.body.insertAdjacentHTML('beforeend', assistantHTML);
    }

    // ربط الأحداث
    setupEventListeners() {
        const close = document.getElementById('closeAssistant');
        const input = document.getElementById('assistantInput');
        const send = document.getElementById('sendMessage');
        const quickBtns = document.querySelectorAll('.quick-btn');

        close.addEventListener('click', () => this.closeAssistant());
        send.addEventListener('click', () => this.sendMessage());
        
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });

        quickBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const question = btn.getAttribute('data-question');
                this.askQuestion(question);
            });
        });
    }

    // تبديل حالة المساعد
    toggleAssistant() {
        const assistant = document.getElementById('ai-assistant');
        this.isOpen = !this.isOpen;
        
        if (this.isOpen) {
            assistant.classList.add('open');
            document.getElementById('assistantInput').focus();
        } else {
            assistant.classList.remove('open');
        }
    }

    // إغلاق المساعد
    closeAssistant() {
        this.isOpen = false;
        document.getElementById('ai-assistant').classList.remove('open');
    }

    // إرسال رسالة
    async sendMessage() {
        const input = document.getElementById('assistantInput');
        const message = input.value.trim();
        
        if (!message) return;
        
        this.addMessage(message, 'user');
        input.value = '';
        
        await this.processMessage(message);
    }

    // طرح سؤال سريع
    async askQuestion(question) {
        this.addMessage(question, 'user');
        await this.processMessage(question);
    }

    // إضافة رسالة للمحادثة
    addMessage(content, sender) {
        const chat = document.getElementById('assistantChat');
        const messageHTML = `
            <div class="message ${sender}-message">
                <div class="message-avatar">${sender === 'user' ? '👤' : '🤖'}</div>
                <div class="message-content">
                    ${typeof content === 'string' ? `<p>${content}</p>` : content}
                </div>
                <div class="message-time">${new Date().toLocaleTimeString('ar-SA')}</div>
            </div>
        `;
        
        chat.insertAdjacentHTML('beforeend', messageHTML);
        chat.scrollTop = chat.scrollHeight;
    }

    // معالجة الرسالة
    async processMessage(message) {
        this.showTypingIndicator();
        
        try {
            const response = await this.generateResponse(message);
            this.hideTypingIndicator();
            this.addMessage(response, 'assistant');
            
            // حفظ المحادثة
            this.conversationHistory.push({
                user: message,
                assistant: response,
                timestamp: new Date().toISOString()
            });
            
        } catch (error) {
            this.hideTypingIndicator();
            this.addMessage('عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.', 'assistant');
        }
    }

    // توليد الرد
    async generateResponse(message) {
        const messageLower = message.toLowerCase();
        
        // تحليل نوع السؤال
        if (this.isGreeting(messageLower)) {
            return this.getGreetingResponse();
        }
        
        if (this.isSearchQuery(messageLower)) {
            return await this.handleSearchQuery(message);
        }
        
        if (this.isComparisonQuery(messageLower)) {
            return this.handleComparisonQuery(message);
        }
        
        if (this.isPromptHelp(messageLower)) {
            return this.getPromptHelp(message);
        }
        
        if (this.isAgentQuery(messageLower)) {
            return this.getAgentInfo(message);
        }
        
        // البحث في قاعدة المعرفة
        const relevantContent = this.searchKnowledgeBase(message);
        if (relevantContent.length > 0) {
            return this.formatKnowledgeResponse(relevantContent, message);
        }
        
        return this.getDefaultResponse(message);
    }

    // التحقق من نوع السؤال
    isGreeting(message) {
        const greetings = ['مرحبا', 'أهلا', 'السلام', 'صباح', 'مساء', 'كيف حالك'];
        return greetings.some(greeting => message.includes(greeting));
    }

    isSearchQuery(message) {
        const searchWords = ['ابحث', 'أبحث', 'أريد', 'أين', 'كيف أجد', 'دلني'];
        return searchWords.some(word => message.includes(word));
    }

    isComparisonQuery(message) {
        const comparisonWords = ['مقارنة', 'قارن', 'الفرق', 'أفضل', 'أحسن', 'vs', 'ضد'];
        return comparisonWords.some(word => message.includes(word));
    }

    isPromptHelp(message) {
        const promptWords = ['برمبت', 'prompt', 'كتابة', 'تحسين', 'فعال'];
        return promptWords.some(word => message.includes(word));
    }

    isAgentQuery(message) {
        const agentWords = ['وكيل', 'agent', 'وكلاء', 'agents', 'ذكي'];
        return agentWords.some(word => message.includes(word));
    }

    // توليد الردود
    getGreetingResponse() {
        const responses = [
            'مرحباً بك! كيف يمكنني مساعدتك في هندسة البرمبت اليوم؟',
            'أهلاً وسهلاً! أنا هنا لمساعدتك في أي استفسار حول البرمبتات والوكلاء الذكيين.',
            'السلام عليكم! يسعدني أن أساعدك في رحلتك لتعلم هندسة البرمبت.'
        ];
        return responses[Math.floor(Math.random() * responses.length)];
    }

    async handleSearchQuery(message) {
        // استخدام نظام البحث الذكي
        if (window.smartSearch) {
            const results = await window.smartSearch.semanticSearch(message);
            if (results.length > 0) {
                let response = `وجدت ${results.length} نتيجة مطابقة لبحثك:\n\n`;
                results.slice(0, 3).forEach((result, index) => {
                    response += `${index + 1}. **${result.title}**\n`;
                    response += `   ${result.summary || 'معلومات مفيدة حول هذا الموضوع'}\n\n`;
                });
                response += 'هل تريد المزيد من التفاصيل حول أي من هذه المواضيع؟';
                return response;
            }
        }
        return 'لم أجد نتائج مطابقة لبحثك. يمكنك تجربة كلمات مفتاحية أخرى أو سؤالي بطريقة مختلفة.';
    }

    handleComparisonQuery(message) {
        if (message.includes('chatgpt') || message.includes('claude') || message.includes('gemini')) {
            return `
**مقارنة النماذج الثلاثة:**

🤖 **ChatGPT (OpenAI):**
- ممتاز في المهام المعقدة والتفكير المنطقي
- يدعم Chain of Thought بشكل قوي
- مناسب للـ Single-Agent والـ Manager-Agent

🧠 **Claude (Anthropic):**
- متفوق في الوعي الأخلاقي والسلامة
- ممتاز في التحليل العميق والفلسفي
- مثالي كـ Guardian-Agent

⚡ **Gemini (Google):**
- سريع جداً في الاستجابة
- كفاءة عالية في الموارد
- مناسب للـ Helper-Agent والمهام البسيطة

هل تريد تفاصيل أكثر حول معيار معين؟
            `;
        }
        return 'يمكنني مساعدتك في مقارنة النماذج المختلفة. حدد لي ما تريد مقارنته بالضبط.';
    }

    getPromptHelp(message) {
        return `
**نصائح لكتابة برمبت فعال:**

1. **الوضوح والتحديد:**
   - كن واضحاً في طلبك
   - حدد السياق والهدف

2. **البنية المنطقية:**
   - ابدأ بالسياق
   - حدد المهمة
   - اذكر التوقعات

3. **استخدم الأمثلة:**
   - قدم أمثلة للتوضيح
   - استخدم Few-shot learning

4. **التحكم في الإخراج:**
   - حدد الطول المطلوب
   - اذكر التنسيق المرغوب

هل تريد مساعدة في تحسين برمبت معين؟
        `;
    }

    getAgentInfo(message) {
        return `
**أنواع الوكلاء الذكيين:**

🤖 **Single-Agent:**
- وكيل واحد ينفذ المهمة كاملة
- مناسب للمهام البسيطة والمتوسطة

👨‍💼 **Manager-Agent:**
- وكيل مدير يقود وكلاء فرعيين
- يوزع المهام ويجمع النتائج

🤝 **Helper-Agent:**
- وكيل مساعد للمهام الفرعية
- متخصص في مهام محددة

🛡️ **Guardian-Agent:**
- وكيل حماية للمراقبة الأخلاقية
- يضمن السلامة والامتثال

أي نوع تريد معرفة المزيد عنه؟
        `;
    }

    // البحث في قاعدة المعرفة
    searchKnowledgeBase(query) {
        const results = [];
        const queryLower = query.toLowerCase();
        
        this.knowledgeBase.forEach((content, id) => {
            let score = 0;
            
            // البحث في العنوان
            if (content.title.toLowerCase().includes(queryLower)) {
                score += 10;
            }
            
            // البحث في الكلمات المفتاحية
            content.keywords.forEach(keyword => {
                if (queryLower.includes(keyword.toLowerCase())) {
                    score += 5;
                }
            });
            
            // البحث في المحتوى
            if (content.content.toLowerCase().includes(queryLower)) {
                score += 3;
            }
            
            if (score > 0) {
                results.push({ ...content, id, score });
            }
        });
        
        return results.sort((a, b) => b.score - a.score).slice(0, 3);
    }

    // تنسيق رد قاعدة المعرفة
    formatKnowledgeResponse(results, query) {
        let response = `وجدت معلومات مفيدة حول "${query}":\n\n`;
        
        results.forEach((result, index) => {
            response += `**${index + 1}. ${result.title}**\n`;
            response += `${result.summary}\n\n`;
        });
        
        response += 'هل تريد المزيد من التفاصيل حول أي من هذه المواضيع؟';
        return response;
    }

    getDefaultResponse(message) {
        const responses = [
            'سؤال مثير للاهتمام! دعني أبحث في قاعدة المعرفة لأجد أفضل إجابة لك.',
            'يمكنني مساعدتك بشكل أفضل إذا كان سؤالك أكثر تحديداً. هل يمكنك إعادة صياغته؟',
            'هذا موضوع معقد. هل يمكنك تحديد الجانب الذي تريد التركيز عليه؟'
        ];
        return responses[Math.floor(Math.random() * responses.length)];
    }

    // مؤشر الكتابة
    showTypingIndicator() {
        this.isTyping = true;
        const typingHTML = `
            <div class="message assistant-message typing-indicator">
                <div class="message-avatar">🤖</div>
                <div class="message-content">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        `;
        
        document.getElementById('assistantChat').insertAdjacentHTML('beforeend', typingHTML);
        document.getElementById('assistantChat').scrollTop = document.getElementById('assistantChat').scrollHeight;
    }

    hideTypingIndicator() {
        this.isTyping = false;
        const typingIndicator = document.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }
}

// تهيئة المساعد الذكي
const aiAssistant = new AIAssistant();

// تصدير للاستخدام العام
window.aiAssistant = aiAssistant;
