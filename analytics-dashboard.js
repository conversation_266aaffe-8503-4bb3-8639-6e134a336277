// لوحة تحكم تحليلية مباشرة
class AnalyticsDashboard {
    constructor() {
        this.metrics = {
            pageViews: 0,
            activeUsers: 0,
            searchQueries: [],
            popularSections: new Map(),
            userInteractions: [],
            performanceMetrics: {},
            realTimeData: {}
        };
        
        this.isVisible = false;
        this.updateInterval = null;
        
        this.init();
    }

    async init() {
        this.createDashboardUI();
        this.setupEventListeners();
        this.startRealTimeTracking();
        this.loadHistoricalData();
        console.log('📊 لوحة التحكم التحليلية جاهزة');
    }

    // إنشاء واجهة لوحة التحكم
    createDashboardUI() {
        const dashboardHTML = `
            <div id="analyticsDashboard" class="analytics-dashboard">
                <div class="dashboard-header">
                    <h2>📊 لوحة التحكم التحليلية</h2>
                    <div class="dashboard-controls">
                        <select id="timeRange">
                            <option value="realtime">الوقت الحالي</option>
                            <option value="today">اليوم</option>
                            <option value="week">هذا الأسبوع</option>
                            <option value="month">هذا الشهر</option>
                        </select>
                        <button id="exportData" class="export-btn">📥 تصدير البيانات</button>
                        <button id="closeDashboard" class="close-btn">✕</button>
                    </div>
                </div>

                <div class="dashboard-content">
                    <!-- مقاييس سريعة -->
                    <div class="quick-metrics">
                        <div class="metric-card">
                            <div class="metric-icon">👥</div>
                            <div class="metric-info">
                                <span class="metric-value" id="activeUsersCount">0</span>
                                <span class="metric-label">مستخدم نشط</span>
                            </div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-icon">👁️</div>
                            <div class="metric-info">
                                <span class="metric-value" id="pageViewsCount">0</span>
                                <span class="metric-label">مشاهدة صفحة</span>
                            </div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-icon">🔍</div>
                            <div class="metric-info">
                                <span class="metric-value" id="searchQueriesCount">0</span>
                                <span class="metric-label">استعلام بحث</span>
                            </div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-icon">⭐</div>
                            <div class="metric-info">
                                <span class="metric-value" id="avgRating">0.0</span>
                                <span class="metric-label">متوسط التقييم</span>
                            </div>
                        </div>
                    </div>

                    <!-- الرسوم البيانية -->
                    <div class="charts-container">
                        <div class="chart-card">
                            <h3>📈 المشاهدات عبر الوقت</h3>
                            <canvas id="viewsChart" width="400" height="200"></canvas>
                        </div>
                        
                        <div class="chart-card">
                            <h3>🏆 الأقسام الأكثر شعبية</h3>
                            <div id="popularSectionsChart" class="bar-chart"></div>
                        </div>
                    </div>

                    <!-- جداول البيانات -->
                    <div class="data-tables">
                        <div class="table-card">
                            <h3>🔍 أحدث عمليات البحث</h3>
                            <div id="recentSearches" class="data-table"></div>
                        </div>
                        
                        <div class="table-card">
                            <h3>👆 التفاعلات الحديثة</h3>
                            <div id="recentInteractions" class="data-table"></div>
                        </div>
                    </div>

                    <!-- إحصائيات الأداء -->
                    <div class="performance-stats">
                        <h3>⚡ إحصائيات الأداء</h3>
                        <div class="performance-grid">
                            <div class="performance-item">
                                <span class="performance-label">سرعة التحميل</span>
                                <span class="performance-value" id="loadTime">0ms</span>
                            </div>
                            <div class="performance-item">
                                <span class="performance-label">وقت الاستجابة</span>
                                <span class="performance-value" id="responseTime">0ms</span>
                            </div>
                            <div class="performance-item">
                                <span class="performance-label">معدل الارتداد</span>
                                <span class="performance-value" id="bounceRate">0%</span>
                            </div>
                            <div class="performance-item">
                                <span class="performance-label">متوسط الجلسة</span>
                                <span class="performance-value" id="sessionDuration">0s</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <button id="dashboardToggle" class="dashboard-toggle" title="لوحة التحكم التحليلية">
                📊
                <span class="live-indicator"></span>
            </button>
        `;

        document.body.insertAdjacentHTML('beforeend', dashboardHTML);
    }

    // ربط الأحداث
    setupEventListeners() {
        // انتظار قليل للتأكد من إنشاء العناصر
        setTimeout(() => {
            const toggle = document.getElementById('dashboardToggle');
            const close = document.getElementById('closeDashboard');
            const timeRange = document.getElementById('timeRange');
            const exportBtn = document.getElementById('exportData');

            if (toggle) {
                console.log('✅ تم العثور على زر لوحة التحكم');
                toggle.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('👆 تم النقر على زر لوحة التحكم');
                    this.toggleDashboard();
                });

                // إضافة حدث إضافي للتأكد
                toggle.onclick = (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('👆 onclick - تم النقر على زر لوحة التحكم');
                    this.toggleDashboard();
                };
            } else {
                console.error('❌ لم يتم العثور على زر لوحة التحكم');
            }

            if (close) {
                close.addEventListener('click', () => this.closeDashboard());
            }

            if (timeRange) {
                timeRange.addEventListener('change', (e) => this.changeTimeRange(e.target.value));
            }

            if (exportBtn) {
                exportBtn.addEventListener('click', () => this.exportData());
            }
        }, 100);

        // تتبع التفاعلات
        this.setupInteractionTracking();
    }

    // تتبع التفاعلات
    setupInteractionTracking() {
        // تتبع النقرات
        document.addEventListener('click', (e) => {
            this.trackInteraction('click', {
                element: e.target.tagName,
                className: e.target.className,
                id: e.target.id,
                timestamp: new Date().toISOString()
            });
        });

        // تتبع التمرير
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                this.trackInteraction('scroll', {
                    scrollY: window.scrollY,
                    scrollPercent: Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100),
                    timestamp: new Date().toISOString()
                });
            }, 100);
        });

        // تتبع الوقت المقضي في الأقسام
        this.setupSectionTracking();
    }

    // تتبع الأقسام
    setupSectionTracking() {
        const sections = document.querySelectorAll('section[data-category]');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const sectionId = entry.target.id;
                    this.trackSectionView(sectionId);
                }
            });
        }, { threshold: 0.5 });

        sections.forEach(section => observer.observe(section));
    }

    // تتبع مشاهدة القسم
    trackSectionView(sectionId) {
        const currentCount = this.metrics.popularSections.get(sectionId) || 0;
        this.metrics.popularSections.set(sectionId, currentCount + 1);
        
        this.updatePopularSectionsChart();
        this.saveMetrics();
    }

    // تتبع التفاعل
    trackInteraction(type, data) {
        this.metrics.userInteractions.push({
            type,
            data,
            timestamp: new Date().toISOString()
        });

        // الاحتفاظ بآخر 100 تفاعل
        if (this.metrics.userInteractions.length > 100) {
            this.metrics.userInteractions = this.metrics.userInteractions.slice(-100);
        }

        this.updateRecentInteractions();
        this.saveMetrics();
    }

    // بدء التتبع المباشر
    startRealTimeTracking() {
        // تحديث المقاييس كل ثانية
        this.updateInterval = setInterval(() => {
            this.updateRealTimeMetrics();
        }, 1000);

        // تتبع الأداء
        this.trackPerformanceMetrics();
    }

    // تحديث المقاييس المباشرة
    updateRealTimeMetrics() {
        // محاكاة المستخدمين النشطين
        this.metrics.activeUsers = Math.floor(Math.random() * 10) + 1;
        
        // تحديث مشاهدات الصفحة
        this.metrics.pageViews++;
        
        // تحديث الواجهة
        this.updateQuickMetrics();
        this.updateViewsChart();
    }

    // تحديث المقاييس السريعة
    updateQuickMetrics() {
        document.getElementById('activeUsersCount').textContent = this.metrics.activeUsers;
        document.getElementById('pageViewsCount').textContent = this.metrics.pageViews;
        document.getElementById('searchQueriesCount').textContent = this.metrics.searchQueries.length;
        
        // حساب متوسط التقييم
        if (window.ratingSystem) {
            const avgRating = this.calculateAverageRating();
            document.getElementById('avgRating').textContent = avgRating.toFixed(1);
        }
    }

    // حساب متوسط التقييم
    calculateAverageRating() {
        if (!window.ratingSystem || !window.ratingSystem.ratings) return 0;
        
        const ratings = Array.from(window.ratingSystem.ratings.values());
        if (ratings.length === 0) return 0;
        
        const totalRating = ratings.reduce((sum, rating) => sum + rating.average, 0);
        return totalRating / ratings.length;
    }

    // تحديث رسم المشاهدات
    updateViewsChart() {
        const canvas = document.getElementById('viewsChart');
        const ctx = canvas.getContext('2d');
        
        // مسح الرسم السابق
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // رسم خط بسيط للمشاهدات
        ctx.strokeStyle = '#4535cd';
        ctx.lineWidth = 2;
        ctx.beginPath();
        
        const dataPoints = this.getViewsDataPoints();
        dataPoints.forEach((point, index) => {
            const x = (index / (dataPoints.length - 1)) * canvas.width;
            const y = canvas.height - (point / Math.max(...dataPoints)) * canvas.height;
            
            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        
        ctx.stroke();
    }

    // الحصول على نقاط بيانات المشاهدات
    getViewsDataPoints() {
        // محاكاة بيانات المشاهدات لآخر 24 ساعة
        const points = [];
        for (let i = 0; i < 24; i++) {
            points.push(Math.floor(Math.random() * 100) + 50);
        }
        return points;
    }

    // تحديث رسم الأقسام الشعبية
    updatePopularSectionsChart() {
        const container = document.getElementById('popularSectionsChart');
        const sections = Array.from(this.metrics.popularSections.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5);

        const maxViews = Math.max(...sections.map(s => s[1]));
        
        container.innerHTML = sections.map(([sectionId, views]) => {
            const sectionTitle = this.getSectionTitle(sectionId);
            const percentage = (views / maxViews) * 100;
            
            return `
                <div class="bar-item">
                    <div class="bar-label">${sectionTitle}</div>
                    <div class="bar-container">
                        <div class="bar-fill" style="width: ${percentage}%"></div>
                        <span class="bar-value">${views}</span>
                    </div>
                </div>
            `;
        }).join('');
    }

    // الحصول على عنوان القسم
    getSectionTitle(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            const title = section.querySelector('h2')?.textContent || sectionId;
            return title.length > 30 ? title.substring(0, 30) + '...' : title;
        }
        return sectionId;
    }

    // تحديث عمليات البحث الحديثة
    updateRecentSearches() {
        const container = document.getElementById('recentSearches');
        const recentSearches = this.metrics.searchQueries.slice(-10).reverse();
        
        container.innerHTML = recentSearches.map(search => `
            <div class="data-row">
                <span class="search-query">${search.query}</span>
                <span class="search-results">${search.results} نتيجة</span>
                <span class="search-time">${new Date(search.timestamp).toLocaleTimeString('ar-SA')}</span>
            </div>
        `).join('') || '<p class="no-data">لا توجد عمليات بحث حديثة</p>';
    }

    // تحديث التفاعلات الحديثة
    updateRecentInteractions() {
        const container = document.getElementById('recentInteractions');
        const recentInteractions = this.metrics.userInteractions.slice(-10).reverse();
        
        container.innerHTML = recentInteractions.map(interaction => `
            <div class="data-row">
                <span class="interaction-type">${this.getInteractionTypeLabel(interaction.type)}</span>
                <span class="interaction-target">${this.getInteractionTarget(interaction.data)}</span>
                <span class="interaction-time">${new Date(interaction.timestamp).toLocaleTimeString('ar-SA')}</span>
            </div>
        `).join('') || '<p class="no-data">لا توجد تفاعلات حديثة</p>';
    }

    // تسمية نوع التفاعل
    getInteractionTypeLabel(type) {
        const labels = {
            'click': '👆 نقرة',
            'scroll': '📜 تمرير',
            'search': '🔍 بحث',
            'rating': '⭐ تقييم'
        };
        return labels[type] || type;
    }

    // هدف التفاعل
    getInteractionTarget(data) {
        if (data.element) {
            return data.element + (data.className ? `.${data.className}` : '');
        }
        if (data.scrollPercent) {
            return `${data.scrollPercent}% من الصفحة`;
        }
        return 'غير محدد';
    }

    // تتبع مقاييس الأداء
    trackPerformanceMetrics() {
        // سرعة التحميل
        window.addEventListener('load', () => {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            this.metrics.performanceMetrics.loadTime = loadTime;
            document.getElementById('loadTime').textContent = loadTime + 'ms';
        });

        // وقت الاستجابة
        const responseTime = performance.timing.responseEnd - performance.timing.requestStart;
        this.metrics.performanceMetrics.responseTime = responseTime;
        document.getElementById('responseTime').textContent = responseTime + 'ms';

        // محاكاة معدل الارتداد ومدة الجلسة
        setTimeout(() => {
            document.getElementById('bounceRate').textContent = '25%';
            document.getElementById('sessionDuration').textContent = '4m 32s';
        }, 2000);
    }

    // تبديل لوحة التحكم
    toggleDashboard() {
        console.log('🔄 تشغيل toggleDashboard');
        const dashboard = document.getElementById('analyticsDashboard');

        if (!dashboard) {
            console.error('❌ لم يتم العثور على عنصر analyticsDashboard');
            return;
        }

        console.log('📊 عنصر لوحة التحكم موجود');
        console.log('📊 الحالة الحالية:', this.isVisible ? 'مفتوح' : 'مغلق');

        this.isVisible = !this.isVisible;

        if (this.isVisible) {
            console.log('✅ فتح لوحة التحكم');
            dashboard.classList.add('open');
            dashboard.style.right = '0';
            this.updateAllCharts();

            // إظهار إشعار
            if (window.enhancedNavManager && window.enhancedNavManager.showNotification) {
                window.enhancedNavManager.showNotification('تم فتح لوحة التحكم التحليلية', 'success');
            }
        } else {
            console.log('❌ إغلاق لوحة التحكم');
            dashboard.classList.remove('open');
            dashboard.style.right = '-100%';
        }

        console.log('📊 الحالة الجديدة:', this.isVisible ? 'مفتوح' : 'مغلق');
        console.log('📊 CSS classes:', dashboard.className);
    }

    // إغلاق لوحة التحكم
    closeDashboard() {
        this.isVisible = false;
        document.getElementById('analyticsDashboard').classList.remove('open');
    }

    // تغيير النطاق الزمني
    changeTimeRange(range) {
        console.log('تغيير النطاق الزمني إلى:', range);
        // يمكن تطوير هذه الوظيفة لتصفية البيانات حسب الوقت
        this.updateAllCharts();
    }

    // تحديث جميع الرسوم البيانية
    updateAllCharts() {
        this.updateQuickMetrics();
        this.updateViewsChart();
        this.updatePopularSectionsChart();
        this.updateRecentSearches();
        this.updateRecentInteractions();
    }

    // تصدير البيانات
    exportData() {
        const data = {
            metrics: this.metrics,
            timestamp: new Date().toISOString(),
            summary: {
                totalPageViews: this.metrics.pageViews,
                totalSearches: this.metrics.searchQueries.length,
                totalInteractions: this.metrics.userInteractions.length,
                averageRating: this.calculateAverageRating()
            }
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `analytics-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        if (window.enhancedNavManager) {
            window.enhancedNavManager.showNotification('تم تصدير البيانات بنجاح', 'success');
        }
    }

    // حفظ المقاييس
    saveMetrics() {
        const dataToSave = {
            pageViews: this.metrics.pageViews,
            searchQueries: this.metrics.searchQueries,
            popularSections: Array.from(this.metrics.popularSections.entries()),
            userInteractions: this.metrics.userInteractions.slice(-50), // حفظ آخر 50 تفاعل
            performanceMetrics: this.metrics.performanceMetrics
        };
        
        localStorage.setItem('analyticsData', JSON.stringify(dataToSave));
    }

    // تحميل البيانات التاريخية
    loadHistoricalData() {
        const savedData = localStorage.getItem('analyticsData');
        if (savedData) {
            const data = JSON.parse(savedData);
            this.metrics.pageViews = data.pageViews || 0;
            this.metrics.searchQueries = data.searchQueries || [];
            this.metrics.popularSections = new Map(data.popularSections || []);
            this.metrics.userInteractions = data.userInteractions || [];
            this.metrics.performanceMetrics = data.performanceMetrics || {};
        }
    }

    // تسجيل استعلام بحث
    logSearchQuery(query, results) {
        this.metrics.searchQueries.push({
            query,
            results,
            timestamp: new Date().toISOString()
        });

        // الاحتفاظ بآخر 100 استعلام
        if (this.metrics.searchQueries.length > 100) {
            this.metrics.searchQueries = this.metrics.searchQueries.slice(-100);
        }

        this.updateRecentSearches();
        this.saveMetrics();
    }
}

// تهيئة لوحة التحكم التحليلية
const analyticsDashboard = new AnalyticsDashboard();

// تصدير للاستخدام العام
window.analyticsDashboard = analyticsDashboard;
