// ملف JavaScript منفصل لتحسين الأداء والتنظيم

// متغيرات عامة
const state = {
    currentSection: null,
    searchTerm: '',
    selectedCategory: 'all',
    selectedComplexity: 'all',
    isSearching: false
};

// وظائف مساعدة
const utils = {
    // تأخير التنفيذ (debounce)
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // تمييز النص المطابق
    highlightText(text, term) {
        if (!term) return text;
        const regex = new RegExp(`(${term})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    },

    // حفظ حالة التطبيق في localStorage
    saveState() {
        localStorage.setItem('promptEngineering_state', JSON.stringify(state));
    },

    // استرجاع حالة التطبيق من localStorage
    loadState() {
        const savedState = localStorage.getItem('promptEngineering_state');
        if (savedState) {
            Object.assign(state, JSON.parse(savedState));
        }
    },

    // تحديث URL بناءً على الحالة الحالية
    updateURL() {
        const params = new URLSearchParams();
        if (state.searchTerm) params.set('search', state.searchTerm);
        if (state.selectedCategory !== 'all') params.set('category', state.selectedCategory);
        if (state.selectedComplexity !== 'all') params.set('complexity', state.selectedComplexity);
        
        const newURL = `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`;
        window.history.replaceState({}, '', newURL);
    },

    // قراءة المعاملات من URL
    readURLParams() {
        const params = new URLSearchParams(window.location.search);
        state.searchTerm = params.get('search') || '';
        state.selectedCategory = params.get('category') || 'all';
        state.selectedComplexity = params.get('complexity') || 'all';
    }
};

// إدارة الأقسام
const sectionManager = {
    // تبديل حالة القسم (مفتوح/مغلق)
    toggle(headerElement) {
        // التحقق من نوع العنصر المرسل
        let section, content, icon;

        if (headerElement.tagName === 'H2') {
            // إذا كان العنصر h2
            section = headerElement.parentElement;
            content = section.querySelector('.content');
            icon = headerElement.querySelector('.toggle-icon');
        } else {
            // إذا كان العنصر section أو عنصر آخر
            section = headerElement.closest('section');
            content = section.querySelector('.content');
            icon = section.querySelector('.toggle-icon');
        }

        if (!section || !content || !icon) {
            console.error('لم يتم العثور على العناصر المطلوبة للقسم');
            return;
        }

        // التحقق من الحالة الحالية
        const isExpanded = section.classList.contains('expanded') ||
                          content.style.display === 'block' ||
                          !section.classList.contains('collapsed');

        if (isExpanded) {
            // إغلاق القسم
            section.classList.remove('expanded');
            section.classList.add('collapsed');
            content.style.display = 'none';
            icon.textContent = '▶';
            icon.style.transform = 'rotate(0deg)';
        } else {
            // فتح القسم
            section.classList.add('expanded');
            section.classList.remove('collapsed');
            content.style.display = 'block';
            icon.textContent = '▼';
            icon.style.transform = 'rotate(90deg)';
        }

        // حفظ حالة القسم في localStorage
        const sectionId = section.id;
        if (section.classList.contains('expanded')) {
            localStorage.setItem(sectionId, 'expanded');
            state.currentSection = sectionId;
        } else {
            localStorage.setItem(sectionId, 'collapsed');
            if (state.currentSection === sectionId) {
                state.currentSection = null;
            }
        }

        utils.saveState();
    },

    // فتح جميع الأقسام
    expandAll() {
        document.querySelectorAll('section').forEach(section => {
            const header = section.querySelector('h2');
            if (header && !section.classList.contains('expanded')) {
                this.toggle(header);
            }
        });
    },

    // إغلاق جميع الأقسام
    collapseAll() {
        document.querySelectorAll('section.expanded').forEach(section => {
            const header = section.querySelector('h2');
            if (header) {
                this.toggle(header);
            }
        });
    },

    // استرجاع حالة الأقسام المحفوظة
    restoreState() {
        // استرجاع حالة كل قسم من localStorage
        document.querySelectorAll('section').forEach(section => {
            const sectionId = section.id;
            const savedState = localStorage.getItem(sectionId);
            const content = section.querySelector('.content');
            const icon = section.querySelector('.toggle-icon');

            if (content && icon) {
                if (savedState === 'expanded') {
                    section.classList.add('expanded');
                    section.classList.remove('collapsed');
                    content.style.display = 'block';
                    icon.textContent = '▼';
                } else {
                    section.classList.remove('expanded');
                    section.classList.add('collapsed');
                    content.style.display = 'none';
                    icon.textContent = '▶';
                }
            }
        });

        // استرجاع القسم النشط الحالي
        if (state.currentSection) {
            const section = document.getElementById(state.currentSection);
            if (section && !section.classList.contains('expanded')) {
                const header = section.querySelector('h2');
                if (header) this.toggle(header);
            }
        }
    }
};

// إدارة البحث والفلترة
const searchManager = {
    elements: {
        searchBox: null,
        filterCategory: null,
        filterComplexity: null,
        loadingIndicator: null,
        sections: null,
        resetButton: null
    },

    // تهيئة عناصر البحث
    init() {
        this.elements.searchBox = document.getElementById('searchBox');
        this.elements.filterCategory = document.getElementById('filterCategory');
        this.elements.filterComplexity = document.getElementById('filterComplexity');
        this.elements.loadingIndicator = document.getElementById('loadingIndicator');
        this.elements.sections = document.querySelectorAll('section[data-category]');
        
        this.createResetButton();
        this.bindEvents();
        this.restoreFilters();
    },

    // إنشاء زر إعادة التعيين
    createResetButton() {
        const resetButton = document.createElement('button');
        resetButton.textContent = 'إعادة تعيين';
        resetButton.className = 'filter-select';
        resetButton.style.backgroundColor = '#f44336';
        resetButton.style.color = 'white';
        resetButton.style.border = 'none';
        resetButton.style.cursor = 'pointer';
        resetButton.addEventListener('click', () => this.reset());
        
        const container = document.querySelector('.search-filter-container');
        if (container) {
            container.appendChild(resetButton);
            this.elements.resetButton = resetButton;
        }
    },

    // ربط الأحداث
    bindEvents() {
        if (this.elements.searchBox) {
            this.elements.searchBox.addEventListener('input', 
                utils.debounce(() => this.performSearch(), 300)
            );
        }
        
        if (this.elements.filterCategory) {
            this.elements.filterCategory.addEventListener('change', () => this.performSearch());
        }
        
        if (this.elements.filterComplexity) {
            this.elements.filterComplexity.addEventListener('change', () => this.performSearch());
        }
    },

    // تنفيذ البحث
    performSearch() {
        if (state.isSearching) return;
        
        state.isSearching = true;
        state.searchTerm = this.elements.searchBox?.value.toLowerCase().trim() || '';
        state.selectedCategory = this.elements.filterCategory?.value || 'all';
        state.selectedComplexity = this.elements.filterComplexity?.value || 'all';

        // إظهار مؤشر التحميل
        if (this.elements.loadingIndicator) {
            this.elements.loadingIndicator.style.display = 'block';
        }

        setTimeout(() => {
            this.filterSections();
            this.updateStats();
            
            // إخفاء مؤشر التحميل
            if (this.elements.loadingIndicator) {
                this.elements.loadingIndicator.style.display = 'none';
            }
            
            state.isSearching = false;
            utils.saveState();
            utils.updateURL();
        }, 200);
    },

    // فلترة الأقسام
    filterSections() {
        if (!this.elements.sections) return;

        this.elements.sections.forEach(section => {
            const keywords = section.getAttribute('data-keywords')?.toLowerCase() || '';
            const category = section.getAttribute('data-category') || '';
            const complexity = section.getAttribute('data-complexity') || '';
            const sectionText = section.textContent.toLowerCase();

            let showSection = true;

            // فلترة البحث النصي
            if (state.searchTerm && 
                !keywords.includes(state.searchTerm) && 
                !sectionText.includes(state.searchTerm)) {
                showSection = false;
            }

            // فلترة الفئة
            if (state.selectedCategory !== 'all' && category !== state.selectedCategory) {
                showSection = false;
            }

            // فلترة مستوى التعقيد
            if (state.selectedComplexity !== 'all' && complexity !== state.selectedComplexity) {
                showSection = false;
            }

            // تطبيق النتيجة
            if (showSection) {
                section.style.display = 'block';
                section.style.opacity = '1';
                section.style.transform = 'translateY(0)';
            } else {
                section.style.display = 'none';
            }
        });
    },

    // تحديث الإحصائيات
    updateStats() {
        if (!this.elements.sections) return;

        const visibleSections = Array.from(this.elements.sections).filter(
            section => section.style.display !== 'none'
        ).length;
        
        const totalSections = this.elements.sections.length;
        
        // يمكن إضافة عنصر UI لعرض الإحصائيات
        console.log(`عرض ${visibleSections} من أصل ${totalSections} قسم`);
        
        // تحديث عنوان الصفحة
        document.title = `بحث هندسة البرمبت - ${visibleSections}/${totalSections} قسم`;
    },

    // إعادة تعيين البحث
    reset() {
        if (this.elements.searchBox) this.elements.searchBox.value = '';
        if (this.elements.filterCategory) this.elements.filterCategory.value = 'all';
        if (this.elements.filterComplexity) this.elements.filterComplexity.value = 'all';
        
        state.searchTerm = '';
        state.selectedCategory = 'all';
        state.selectedComplexity = 'all';
        
        this.filterSections();
        this.updateStats();
        utils.saveState();
        utils.updateURL();
    },

    // استرجاع حالة الفلاتر
    restoreFilters() {
        if (this.elements.searchBox && state.searchTerm) {
            this.elements.searchBox.value = state.searchTerm;
        }
        if (this.elements.filterCategory && state.selectedCategory) {
            this.elements.filterCategory.value = state.selectedCategory;
        }
        if (this.elements.filterComplexity && state.selectedComplexity) {
            this.elements.filterComplexity.value = state.selectedComplexity;
        }
        
        // تطبيق الفلاتر المحفوظة
        if (state.searchTerm || state.selectedCategory !== 'all' || state.selectedComplexity !== 'all') {
            this.performSearch();
        }
    }
};

// إدارة الشريط العلوي والتنقل المحسّن
const enhancedNavManager = {
    // تبديل الوضع المظلم
    toggleDarkMode() {
        document.body.classList.toggle('dark-mode');
        const isDark = document.body.classList.contains('dark-mode');

        // تحديث أيقونة الوضع المظلم
        const themeIcon = document.querySelector('.theme-icon');
        if (themeIcon) {
            themeIcon.textContent = isDark ? '☀️' : '🌙';
        }

        // حفظ التفضيل
        localStorage.setItem('darkMode', isDark);
    },

    // تبديل قائمة المستخدم
    toggleUserMenu() {
        const dropdown = document.getElementById('userDropdown');
        if (dropdown) {
            dropdown.classList.toggle('show');
        }
    },

    // تبديل القائمة المحمولة
    toggleMobileMenu() {
        const mobileToggle = document.getElementById('mobile-menu');
        const navMenu = document.querySelector('.nav-menu');

        if (mobileToggle && navMenu) {
            mobileToggle.classList.toggle('open');
            navMenu.classList.toggle('show');
        }
    },

    // البحث السريع
    openQuickSearch() {
        const searchBox = document.getElementById('searchBox');
        if (searchBox) {
            searchBox.focus();
            searchBox.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    },

    // إضافة إلى المفضلة
    toggleBookmark() {
        const bookmarkBtn = document.getElementById('bookmarkBtn');
        const isBookmarked = bookmarkBtn.classList.contains('bookmarked');

        if (isBookmarked) {
            bookmarkBtn.classList.remove('bookmarked');
            bookmarkBtn.title = 'إضافة إلى المفضلة';
            this.showNotification('تم إزالة الصفحة من المفضلة', 'info');
        } else {
            bookmarkBtn.classList.add('bookmarked');
            bookmarkBtn.title = 'إزالة من المفضلة';
            this.showNotification('تم إضافة الصفحة إلى المفضلة', 'success');
        }

        // حفظ حالة المفضلة
        localStorage.setItem('isBookmarked', !isBookmarked);
    },

    // مشاركة الصفحة
    shareePage() {
        if (navigator.share) {
            navigator.share({
                title: document.title,
                text: 'بحث هندسة البرمبت وبناء الوكلاء الذكيين',
                url: window.location.href
            });
        } else {
            // نسخ الرابط إلى الحافظة
            navigator.clipboard.writeText(window.location.href).then(() => {
                this.showNotification('تم نسخ الرابط إلى الحافظة', 'success');
            });
        }
    },

    // ملء الشاشة
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    },

    // عرض الإشعارات
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        // إضافة الإشعار إلى الصفحة
        document.body.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => notification.classList.add('show'), 100);

        // إخفاء الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    },

    // تهيئة الشريط العلوي
    init() {
        // تحميل الوضع المظلم المحفوظ
        const savedDarkMode = localStorage.getItem('darkMode') === 'true';
        if (savedDarkMode) {
            document.body.classList.add('dark-mode');
            const themeIcon = document.querySelector('.theme-icon');
            if (themeIcon) themeIcon.textContent = '☀️';
        }

        // تحميل حالة المفضلة
        const isBookmarked = localStorage.getItem('isBookmarked') === 'true';
        const bookmarkBtn = document.getElementById('bookmarkBtn');
        if (isBookmarked && bookmarkBtn) {
            bookmarkBtn.classList.add('bookmarked');
            bookmarkBtn.title = 'إزالة من المفضلة';
        }

        // ربط الأحداث
        this.bindEvents();
    },

    // ربط الأحداث
    bindEvents() {
        // زر الوضع المظلم
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleDarkMode());
        }

        // قائمة المستخدم
        const userMenuBtn = document.getElementById('userMenuBtn');
        if (userMenuBtn) {
            userMenuBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleUserMenu();
            });
        }

        // إغلاق قائمة المستخدم عند النقر خارجها
        document.addEventListener('click', () => {
            const dropdown = document.getElementById('userDropdown');
            if (dropdown) dropdown.classList.remove('show');
        });

        // القائمة المحمولة
        const mobileMenuToggle = document.getElementById('mobile-menu');
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => this.toggleMobileMenu());
        }

        // أزرار الإجراءات
        const quickSearchBtn = document.getElementById('quickSearchBtn');
        if (quickSearchBtn) {
            quickSearchBtn.addEventListener('click', () => this.openQuickSearch());
        }

        const bookmarkBtn = document.getElementById('bookmarkBtn');
        if (bookmarkBtn) {
            bookmarkBtn.addEventListener('click', () => this.toggleBookmark());
        }

        const shareBtn = document.getElementById('shareBtn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => this.shareePage());
        }

        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
        }

        // تغيير اللغة
        const languageSelect = document.getElementById('languageSelect');
        if (languageSelect) {
            languageSelect.addEventListener('change', (e) => {
                this.changeLanguage(e.target.value);
            });
        }
    },

    // تغيير اللغة
    changeLanguage(lang) {
        // حفظ اللغة المختارة
        localStorage.setItem('selectedLanguage', lang);

        // إظهار إشعار (يمكن تطوير هذه الوظيفة لاحقاً)
        this.showNotification(`تم تغيير اللغة إلى ${lang === 'ar' ? 'العربية' : lang === 'en' ? 'English' : 'Français'}`, 'info');
    }
};

// تهيئة التطبيق
const app = {
    init() {
        // قراءة المعاملات من URL
        utils.readURLParams();

        // تحميل الحالة المحفوظة
        utils.loadState();

        // تهيئة المكونات
        searchManager.init();
        enhancedNavManager.init();
        sectionManager.restoreState();

        // ربط الأحداث العامة
        this.bindGlobalEvents();

        console.log('تم تحميل التطبيق بنجاح');
    },

    bindGlobalEvents() {
        // زر العودة للأعلى
        const backToTopBtn = document.getElementById('backToTop');
        if (backToTopBtn) {
            backToTopBtn.addEventListener('click', () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        }

        // ربط أحداث تبديل الأقسام (فقط إذا لم تكن مربوطة مسبقاً)
        document.querySelectorAll('section h2').forEach(header => {
            // إزالة الحدث القديم إذا كان موجوداً
            header.removeAttribute('onclick');

            // إضافة الحدث الجديد
            header.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                sectionManager.toggle(header);
            });
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K للبحث
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                enhancedNavManager.openQuickSearch();
            }

            // Escape لإعادة تعيين البحث
            if (e.key === 'Escape') {
                searchManager.reset();
            }

            // F للبحث السريع
            if (e.key === 'f' && !e.ctrlKey && !e.metaKey) {
                const activeElement = document.activeElement;
                if (activeElement.tagName !== 'INPUT' && activeElement.tagName !== 'TEXTAREA') {
                    e.preventDefault();
                    enhancedNavManager.openQuickSearch();
                }
            }
        });

        // حفظ الحالة عند إغلاق الصفحة
        window.addEventListener('beforeunload', () => {
            utils.saveState();
        });

        // تتبع التمرير لإظهار/إخفاء زر العودة للأعلى
        window.addEventListener('scroll', utils.debounce(() => {
            const backBtn = document.getElementById('backToTop');
            if (backBtn) {
                if (window.scrollY > 300) {
                    backBtn.style.display = 'block';
                } else {
                    backBtn.style.display = 'none';
                }
            }
        }, 100));
    }
};

// تشغيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    app.init();
});

// تصدير الوظائف للاستخدام العام
window.toggleSection = (headerElement) => sectionManager.toggle(headerElement);
window.searchManager = searchManager;
window.sectionManager = sectionManager;
window.enhancedNavManager = enhancedNavManager;
window.app = app;
