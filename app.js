// ملف JavaScript منفصل لتحسين الأداء والتنظيم

// متغيرات عامة
const state = {
    currentSection: null,
    searchTerm: '',
    selectedCategory: 'all',
    selectedComplexity: 'all',
    isSearching: false
};

// وظائف مساعدة
const utils = {
    // تأخير التنفيذ (debounce)
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // تمييز النص المطابق
    highlightText(text, term) {
        if (!term) return text;
        const regex = new RegExp(`(${term})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    },

    // حفظ حالة التطبيق في localStorage
    saveState() {
        localStorage.setItem('promptEngineering_state', JSON.stringify(state));
    },

    // استرجاع حالة التطبيق من localStorage
    loadState() {
        const savedState = localStorage.getItem('promptEngineering_state');
        if (savedState) {
            Object.assign(state, JSON.parse(savedState));
        }
    },

    // تحديث URL بناءً على الحالة الحالية
    updateURL() {
        const params = new URLSearchParams();
        if (state.searchTerm) params.set('search', state.searchTerm);
        if (state.selectedCategory !== 'all') params.set('category', state.selectedCategory);
        if (state.selectedComplexity !== 'all') params.set('complexity', state.selectedComplexity);
        
        const newURL = `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`;
        window.history.replaceState({}, '', newURL);
    },

    // قراءة المعاملات من URL
    readURLParams() {
        const params = new URLSearchParams(window.location.search);
        state.searchTerm = params.get('search') || '';
        state.selectedCategory = params.get('category') || 'all';
        state.selectedComplexity = params.get('complexity') || 'all';
    }
};

// إدارة الأقسام
const sectionManager = {
    // تبديل حالة القسم (مفتوح/مغلق)
    toggle(headerElement) {
        const section = headerElement.parentElement;
        const content = section.querySelector('.content');
        const icon = headerElement.querySelector('.toggle-icon');
        
        if (section.classList.contains('expanded')) {
            // إغلاق القسم
            section.classList.remove('expanded');
            content.style.display = 'none';
            icon.textContent = '▶';
            icon.style.transform = 'rotate(0deg)';
        } else {
            // فتح القسم
            section.classList.add('expanded');
            content.style.display = 'block';
            icon.textContent = '▼';
            icon.style.transform = 'rotate(90deg)';
        }
        
        // حفظ حالة القسم
        state.currentSection = section.classList.contains('expanded') ? section.id : null;
        utils.saveState();
    },

    // فتح جميع الأقسام
    expandAll() {
        document.querySelectorAll('section').forEach(section => {
            const header = section.querySelector('h2');
            if (header && !section.classList.contains('expanded')) {
                this.toggle(header);
            }
        });
    },

    // إغلاق جميع الأقسام
    collapseAll() {
        document.querySelectorAll('section.expanded').forEach(section => {
            const header = section.querySelector('h2');
            if (header) {
                this.toggle(header);
            }
        });
    },

    // استرجاع حالة الأقسام المحفوظة
    restoreState() {
        if (state.currentSection) {
            const section = document.getElementById(state.currentSection);
            if (section && !section.classList.contains('expanded')) {
                const header = section.querySelector('h2');
                if (header) this.toggle(header);
            }
        }
    }
};

// إدارة البحث والفلترة
const searchManager = {
    elements: {
        searchBox: null,
        filterCategory: null,
        filterComplexity: null,
        loadingIndicator: null,
        sections: null,
        resetButton: null
    },

    // تهيئة عناصر البحث
    init() {
        this.elements.searchBox = document.getElementById('searchBox');
        this.elements.filterCategory = document.getElementById('filterCategory');
        this.elements.filterComplexity = document.getElementById('filterComplexity');
        this.elements.loadingIndicator = document.getElementById('loadingIndicator');
        this.elements.sections = document.querySelectorAll('section[data-category]');
        
        this.createResetButton();
        this.bindEvents();
        this.restoreFilters();
    },

    // إنشاء زر إعادة التعيين
    createResetButton() {
        const resetButton = document.createElement('button');
        resetButton.textContent = 'إعادة تعيين';
        resetButton.className = 'filter-select';
        resetButton.style.backgroundColor = '#f44336';
        resetButton.style.color = 'white';
        resetButton.style.border = 'none';
        resetButton.style.cursor = 'pointer';
        resetButton.addEventListener('click', () => this.reset());
        
        const container = document.querySelector('.search-filter-container');
        if (container) {
            container.appendChild(resetButton);
            this.elements.resetButton = resetButton;
        }
    },

    // ربط الأحداث
    bindEvents() {
        if (this.elements.searchBox) {
            this.elements.searchBox.addEventListener('input', 
                utils.debounce(() => this.performSearch(), 300)
            );
        }
        
        if (this.elements.filterCategory) {
            this.elements.filterCategory.addEventListener('change', () => this.performSearch());
        }
        
        if (this.elements.filterComplexity) {
            this.elements.filterComplexity.addEventListener('change', () => this.performSearch());
        }
    },

    // تنفيذ البحث
    performSearch() {
        if (state.isSearching) return;
        
        state.isSearching = true;
        state.searchTerm = this.elements.searchBox?.value.toLowerCase().trim() || '';
        state.selectedCategory = this.elements.filterCategory?.value || 'all';
        state.selectedComplexity = this.elements.filterComplexity?.value || 'all';

        // إظهار مؤشر التحميل
        if (this.elements.loadingIndicator) {
            this.elements.loadingIndicator.style.display = 'block';
        }

        setTimeout(() => {
            this.filterSections();
            this.updateStats();
            
            // إخفاء مؤشر التحميل
            if (this.elements.loadingIndicator) {
                this.elements.loadingIndicator.style.display = 'none';
            }
            
            state.isSearching = false;
            utils.saveState();
            utils.updateURL();
        }, 200);
    },

    // فلترة الأقسام
    filterSections() {
        if (!this.elements.sections) return;

        this.elements.sections.forEach(section => {
            const keywords = section.getAttribute('data-keywords')?.toLowerCase() || '';
            const category = section.getAttribute('data-category') || '';
            const complexity = section.getAttribute('data-complexity') || '';
            const sectionText = section.textContent.toLowerCase();

            let showSection = true;

            // فلترة البحث النصي
            if (state.searchTerm && 
                !keywords.includes(state.searchTerm) && 
                !sectionText.includes(state.searchTerm)) {
                showSection = false;
            }

            // فلترة الفئة
            if (state.selectedCategory !== 'all' && category !== state.selectedCategory) {
                showSection = false;
            }

            // فلترة مستوى التعقيد
            if (state.selectedComplexity !== 'all' && complexity !== state.selectedComplexity) {
                showSection = false;
            }

            // تطبيق النتيجة
            if (showSection) {
                section.style.display = 'block';
                section.style.opacity = '1';
                section.style.transform = 'translateY(0)';
            } else {
                section.style.display = 'none';
            }
        });
    },

    // تحديث الإحصائيات
    updateStats() {
        if (!this.elements.sections) return;

        const visibleSections = Array.from(this.elements.sections).filter(
            section => section.style.display !== 'none'
        ).length;
        
        const totalSections = this.elements.sections.length;
        
        // يمكن إضافة عنصر UI لعرض الإحصائيات
        console.log(`عرض ${visibleSections} من أصل ${totalSections} قسم`);
        
        // تحديث عنوان الصفحة
        document.title = `بحث هندسة البرمبت - ${visibleSections}/${totalSections} قسم`;
    },

    // إعادة تعيين البحث
    reset() {
        if (this.elements.searchBox) this.elements.searchBox.value = '';
        if (this.elements.filterCategory) this.elements.filterCategory.value = 'all';
        if (this.elements.filterComplexity) this.elements.filterComplexity.value = 'all';
        
        state.searchTerm = '';
        state.selectedCategory = 'all';
        state.selectedComplexity = 'all';
        
        this.filterSections();
        this.updateStats();
        utils.saveState();
        utils.updateURL();
    },

    // استرجاع حالة الفلاتر
    restoreFilters() {
        if (this.elements.searchBox && state.searchTerm) {
            this.elements.searchBox.value = state.searchTerm;
        }
        if (this.elements.filterCategory && state.selectedCategory) {
            this.elements.filterCategory.value = state.selectedCategory;
        }
        if (this.elements.filterComplexity && state.selectedComplexity) {
            this.elements.filterComplexity.value = state.selectedComplexity;
        }
        
        // تطبيق الفلاتر المحفوظة
        if (state.searchTerm || state.selectedCategory !== 'all' || state.selectedComplexity !== 'all') {
            this.performSearch();
        }
    }
};

// تصدير الوظائف للاستخدام العام
window.toggleSection = (headerElement) => sectionManager.toggle(headerElement);
window.searchManager = searchManager;
window.sectionManager = sectionManager;
