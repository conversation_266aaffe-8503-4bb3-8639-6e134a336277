{"project": {"name": "بحث هندسة البرمبت وبناء الوكلاء الذكيين", "version": "2.0.0", "description": "صفحة ويب تفاعلية شاملة لاستكشاف هندسة البرمبت وتصميم الوكلاء الذكيين", "author": "Augment Agent", "license": "MIT"}, "features": {"search": {"enabled": true, "debounceDelay": 300, "minSearchLength": 2, "highlightResults": true}, "filters": {"categories": ["all", "prompt", "agents", "evaluation", "tools", "database", "examples"], "complexity": ["all", "basic", "intermediate", "advanced"]}, "ui": {"animations": true, "darkMode": false, "rtlSupport": true, "responsiveBreakpoint": 768}, "performance": {"lazyLoading": true, "caching": true, "compression": true}}, "sections": [{"id": "section1", "title": "المقدمة والغرض من البحث", "category": "prompt", "complexity": "basic", "keywords": ["مقدمة", "غرض", "ب<PERSON><PERSON>", "هندسة", "برمبت", "وكلاء", "ذكيين"]}, {"id": "section2", "title": "تعريف مكونات هندسة البرمبت الأساسية", "category": "prompt", "complexity": "intermediate", "keywords": ["مكونات", "هندسة", "برمبت", "بنية", "نبرة", "تعقيد", "وضوح", "تحكم", "إخ<PERSON><PERSON>ج", "ضوابط"]}, {"id": "section3", "title": "تحليل استجابات النماذج الثلاثة ودمج مبادئ بناء الوكلاء", "category": "evaluation", "complexity": "advanced", "keywords": ["تحليل", "استجابات", "نماذج", "ChatGPT", "Gemini", "<PERSON>", "وكلاء", "مقارنة", "أداء"]}, {"id": "section4", "title": "جمع وتصنيف أمثلة برمبت ووكلاء من الإنترنت والمجتمعات", "category": "examples", "complexity": "intermediate", "keywords": ["جمع", "تصنيف", "أمثلة", "برمبت", "وكلاء", "إنترنت", "مجتمعات", "Reddit", "GitHub"]}, {"id": "section5", "title": "بناء قاعدة بيانات برمبتات ووكلاء مصنّفة", "category": "database", "complexity": "advanced", "keywords": ["قاعدة", "بيانات", "برمبتات", "وكلاء", "مصنفة", "هيكل", "جدول", "schema"]}, {"id": "section6", "title": "تصميم نظام مقارنة آلي", "category": "evaluation", "complexity": "advanced", "keywords": ["نظام", "مقارنة", "آلي", "تقييم", "معايير", "دقة", "إبداع", "وضوح", "كفاءة"]}, {"id": "section7", "title": "اختبار أساليب مختلفة في صياغة البرمبت وإنشاء وكيل موجه", "category": "prompt", "complexity": "intermediate", "keywords": ["أساليب", "صياغة", "برمبت", "وكيل", "موجه", "<PERSON><PERSON><PERSON>", "مبا<PERSON>ر", "حواري", "أدوار"]}, {"id": "section8", "title": "إنشاء أداة توليد أوتوماتيكي لبرمبتات محترفة وتوجيه الوكلاء", "category": "tools", "complexity": "advanced", "keywords": ["أداة", "توليد", "أوتوماتيكي", "برمبتات", "محترفة", "وكلاء", "Python", "سكربت"]}, {"id": "section9", "title": "إنشاء أداة تصنيف تلقائي للبرمبتات والوكيل", "category": "tools", "complexity": "advanced", "keywords": ["أداة", "تصنيف", "تلقائي", "برمبتات", "وكيل", "BERT", "نموذج", "تصنيفي"]}, {"id": "section10", "title": "تطوير دليل برمبتات ووكلاء احترافي لكل نموذج", "category": "examples", "complexity": "intermediate", "keywords": ["دليل", "برمبتات", "وكلاء", "احترافي", "نموذج", "تجاري", "أكاديمي", "برمجي"]}, {"id": "section11", "title": "Dashboard تفاعلي", "category": "tools", "complexity": "advanced", "keywords": ["Dashboard", "تفاعلي", "واجهة", "إدارة", "تحكم", "إحصائيات"]}, {"id": "section12", "title": "متغيرات التخصيص", "category": "tools", "complexity": "intermediate", "keywords": ["متغيرات", "تخصيص", "إعدادات", "تكوين", "خيارات"]}], "models": {"chatgpt": {"name": "OpenAI ChatGPT", "strengths": ["دقة عالية", "CoT", "Self-Consistency", "مهام متعددة الخطوات"], "weaknesses": ["استهلاك طاقة", "تكلفة"], "bestFor": ["Single-Agent", "Manager-Agent", "مهام معقدة"]}, "gemini": {"name": "Google Gemini 2.5", "strengths": ["سرعة عالية", "كفاءة", "استجابة سريعة"], "weaknesses": ["<PERSON><PERSON><PERSON> أقل", "مرونة محدودة"], "bestFor": ["Helper-Agent", "مهام بسيطة", "استرجاع سريع"]}, "claude": {"name": "Anthropic Claude 4", "strengths": ["وعي أخلاقي", "سياق عميق", "ReAct", "تحليل فلسفي"], "weaknesses": ["بطء نسبي", "إطالة في الردود"], "bestFor": ["Guardian-Agent", "مراجعة أخلاقية", "تحليل معقد"]}}, "evaluation": {"criteria": [{"name": "accuracy", "weight": 0.25, "description": "مدى صحة المعلومات"}, {"name": "creativity", "weight": 0.2, "description": "قدرة على تقديم أفكار جديدة"}, {"name": "clarity", "weight": 0.2, "description": "سهولة فهم النص وتنظيمه"}, {"name": "efficiency", "weight": 0.15, "description": "طول الاستجابة وملاءمتها"}, {"name": "compliance", "weight": 0.1, "description": "التزام بالتعليمات"}, {"name": "ethicalAlignment", "weight": 0.1, "description": "احترام الضوابط الأخلاقية"}]}, "development": {"environment": "production", "debugging": false, "analytics": true, "errorReporting": true}}