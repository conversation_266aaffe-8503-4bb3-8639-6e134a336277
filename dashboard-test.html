<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار لوحة التحكم التحليلية</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            margin: 20px;
            background: #f5f5f5;
            padding-right: 80px; /* مساحة لزر لوحة التحكم */
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4535cd;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #3a2ba8;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .error {
            background: #ffe8e8;
            border-left-color: #f44336;
        }
        .info {
            background: #e3f2fd;
            border-left-color: #2196f3;
        }
        
        /* أنماط لوحة التحكم */
        .analytics-dashboard {
            position: fixed;
            top: 0;
            right: -100%;
            width: 400px;
            height: 100vh;
            background: white;
            z-index: 10002;
            transition: right 0.3s ease;
            overflow-y: auto;
            box-shadow: -5px 0 20px rgba(0,0,0,0.1);
            border-left: 1px solid #ddd;
        }
        
        .analytics-dashboard.open {
            right: 0;
        }
        
        .dashboard-toggle {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #4535cd, #6c5ce7);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 20px;
            cursor: pointer;
            z-index: 9998;
            box-shadow: 0 4px 15px rgba(69, 53, 205, 0.3);
            transition: all 0.3s ease;
        }
        
        .dashboard-toggle:hover {
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(69, 53, 205, 0.4);
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #4535cd, #6c5ce7);
            color: white;
            padding: 20px;
            position: sticky;
            top: 0;
        }
        
        .dashboard-content {
            padding: 20px;
        }
        
        .close-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            float: left;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار لوحة التحكم التحليلية</h1>
    
    <div class="test-section">
        <h2>معلومات الاختبار</h2>
        <p>هذه الصفحة مخصصة لاختبار لوحة التحكم التحليلية بشكل منفصل.</p>
        <p>يجب أن ترى زر لوحة التحكم على الجانب الأيمن من الشاشة.</p>
    </div>

    <div class="test-section">
        <h2>اختبارات لوحة التحكم</h2>
        <button class="test-button" onclick="testDashboardButton()">اختبار وجود الزر</button>
        <button class="test-button" onclick="testDashboardPanel()">اختبار وجود اللوحة</button>
        <button class="test-button" onclick="testToggleFunction()">اختبار وظيفة التبديل</button>
        <button class="test-button" onclick="manualToggle()">تبديل يدوي</button>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>معلومات التشخيص</h2>
        <div id="diagnostic-info"></div>
    </div>

    <!-- لوحة التحكم التحليلية -->
    <div id="analyticsDashboard" class="analytics-dashboard">
        <div class="dashboard-header">
            <h2>📊 لوحة التحكم التحليلية</h2>
            <button class="close-btn" onclick="closeDashboard()">✕</button>
        </div>
        <div class="dashboard-content">
            <h3>🎉 تعمل لوحة التحكم بنجاح!</h3>
            <p>إذا كنت ترى هذه الرسالة، فهذا يعني أن لوحة التحكم تعمل بشكل صحيح.</p>
            
            <div style="margin: 20px 0;">
                <h4>📊 بيانات تجريبية:</h4>
                <ul>
                    <li>المستخدمون النشطون: 5</li>
                    <li>مشاهدات الصفحة: 127</li>
                    <li>استعلامات البحث: 23</li>
                    <li>متوسط التقييم: 4.2</li>
                </ul>
            </div>
            
            <div style="margin: 20px 0;">
                <h4>🔧 حالة النظام:</h4>
                <p>✅ جميع الأنظمة تعمل بشكل طبيعي</p>
            </div>
        </div>
    </div>

    <!-- زر لوحة التحكم -->
    <button id="dashboardToggle" class="dashboard-toggle" title="لوحة التحكم التحليلية">
        📊
    </button>

    <!-- ربط الملفات -->
    <script src="analytics-dashboard.js"></script>

    <script>
        // وظائف الاختبار
        function addResult(message, type = 'info') {
            const container = document.getElementById('test-results');
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        function testDashboardButton() {
            const container = document.getElementById('test-results');
            container.innerHTML = '';
            
            const button = document.getElementById('dashboardToggle');
            if (button) {
                addResult('✅ زر لوحة التحكم موجود', 'result');
                addResult(`📍 موقع الزر: ${button.style.position || getComputedStyle(button).position}`, 'info');
                addResult(`🎨 لون الخلفية: ${getComputedStyle(button).backgroundColor}`, 'info');
            } else {
                addResult('❌ زر لوحة التحكم غير موجود', 'error');
            }
        }

        function testDashboardPanel() {
            const panel = document.getElementById('analyticsDashboard');
            if (panel) {
                addResult('✅ لوحة التحكم موجودة', 'result');
                addResult(`📍 موقع اللوحة: right = ${getComputedStyle(panel).right}`, 'info');
                addResult(`🎨 حالة اللوحة: ${panel.classList.contains('open') ? 'مفتوحة' : 'مغلقة'}`, 'info');
            } else {
                addResult('❌ لوحة التحكم غير موجودة', 'error');
            }
        }

        function testToggleFunction() {
            if (window.analyticsDashboard && typeof window.analyticsDashboard.toggleDashboard === 'function') {
                addResult('✅ وظيفة toggleDashboard موجودة', 'result');
                try {
                    window.analyticsDashboard.toggleDashboard();
                    addResult('✅ تم تشغيل وظيفة toggleDashboard بنجاح', 'result');
                } catch (error) {
                    addResult(`❌ خطأ في تشغيل toggleDashboard: ${error.message}`, 'error');
                }
            } else {
                addResult('❌ وظيفة toggleDashboard غير متاحة', 'error');
            }
        }

        function manualToggle() {
            const panel = document.getElementById('analyticsDashboard');
            if (panel) {
                const isOpen = panel.classList.contains('open');
                if (isOpen) {
                    panel.classList.remove('open');
                    panel.style.right = '-100%';
                    addResult('✅ تم إغلاق اللوحة يدوياً', 'result');
                } else {
                    panel.classList.add('open');
                    panel.style.right = '0';
                    addResult('✅ تم فتح اللوحة يدوياً', 'result');
                }
            }
        }

        function closeDashboard() {
            const panel = document.getElementById('analyticsDashboard');
            if (panel) {
                panel.classList.remove('open');
                panel.style.right = '-100%';
            }
        }

        function updateDiagnosticInfo() {
            const container = document.getElementById('diagnostic-info');
            const info = {
                'window.analyticsDashboard': window.analyticsDashboard ? '✅ موجود' : '❌ غير موجود',
                'زر لوحة التحكم': document.getElementById('dashboardToggle') ? '✅ موجود' : '❌ غير موجود',
                'لوحة التحكم': document.getElementById('analyticsDashboard') ? '✅ موجودة' : '❌ غير موجودة',
                'حالة اللوحة': document.getElementById('analyticsDashboard')?.classList.contains('open') ? 'مفتوحة' : 'مغلقة',
                'عرض الشاشة': window.innerWidth + 'px',
                'ارتفاع الشاشة': window.innerHeight + 'px'
            };

            container.innerHTML = Object.entries(info).map(([key, value]) => 
                `<p><strong>${key}:</strong> ${value}</p>`
            ).join('');
        }

        // تشغيل التشخيص عند التحميل
        window.addEventListener('load', () => {
            updateDiagnosticInfo();
            
            // تحديث التشخيص كل ثانية
            setInterval(updateDiagnosticInfo, 1000);
            
            // اختبار تلقائي
            setTimeout(() => {
                testDashboardButton();
                testDashboardPanel();
            }, 500);
        });

        // إضافة حدث للزر مباشرة
        document.addEventListener('DOMContentLoaded', () => {
            const button = document.getElementById('dashboardToggle');
            if (button) {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('🔄 تم النقر على الزر (حدث مباشر)');
                    manualToggle();
                });
            }
        });
    </script>
</body>
</html>
