<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار وظائف الموقع</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4535cd;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #3a2ba8;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .error {
            background: #ffe8e8;
            border-left-color: #f44336;
        }
        section {
            border: 1px solid #ddd;
            margin: 10px 0;
            border-radius: 5px;
        }
        section.collapsed .content {
            display: none;
        }
        section h2 {
            background: #f0f0f0;
            margin: 0;
            padding: 15px;
            cursor: pointer;
            border-radius: 5px 5px 0 0;
        }
        .content {
            padding: 15px;
        }
        .toggle-icon {
            float: left;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار وظائف الموقع</h1>
    
    <div class="test-section">
        <h2>اختبار وظيفة طي/فتح الأقسام</h2>
        <button class="test-button" onclick="testToggleFunction()">اختبار toggleSection</button>
        <button class="test-button" onclick="testSectionManager()">اختبار sectionManager</button>
        <div id="toggle-results"></div>
    </div>

    <div class="test-section">
        <h2>اختبار الأقسام</h2>
        
        <section id="test-section1" class="collapsed" data-category="test" data-complexity="basic">
            <h2 onclick="toggleSection(this)">
                <span>قسم اختبار 1</span>
                <span class="toggle-icon">▶</span>
            </h2>
            <div class="content">
                <p>هذا محتوى القسم الأول للاختبار.</p>
            </div>
        </section>

        <section id="test-section2" class="collapsed" data-category="test" data-complexity="intermediate">
            <h2 onclick="toggleSection(this)">
                <span>قسم اختبار 2</span>
                <span class="toggle-icon">▶</span>
            </h2>
            <div class="content">
                <p>هذا محتوى القسم الثاني للاختبار.</p>
            </div>
        </section>
    </div>

    <div class="test-section">
        <h2>اختبار المكونات الأخرى</h2>
        <button class="test-button" onclick="testSmartSearch()">اختبار البحث الذكي</button>
        <button class="test-button" onclick="testAIAssistant()">اختبار المساعد الذكي</button>
        <button class="test-button" onclick="testRatingSystem()">اختبار نظام التقييم</button>
        <button class="test-button" onclick="testAnalytics()">اختبار التحليلات</button>
        <div id="component-results"></div>
    </div>

    <!-- ربط الملفات -->
    <script src="app.js"></script>
    <script src="smart-search.js"></script>
    <script src="ai-assistant.js"></script>
    <script src="rating-system.js"></script>
    <script src="analytics-dashboard.js"></script>

    <script>
        // وظائف الاختبار
        function addResult(containerId, message, isError = false) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = isError ? 'result error' : 'result';
            result.textContent = message;
            container.appendChild(result);
        }

        function testToggleFunction() {
            const container = document.getElementById('toggle-results');
            container.innerHTML = '';
            
            try {
                // اختبار وجود الوظيفة
                if (typeof toggleSection === 'function') {
                    addResult('toggle-results', '✅ وظيفة toggleSection موجودة');
                } else {
                    addResult('toggle-results', '❌ وظيفة toggleSection غير موجودة', true);
                    return;
                }

                // اختبار تشغيل الوظيفة
                const testHeader = document.querySelector('#test-section1 h2');
                if (testHeader) {
                    toggleSection(testHeader);
                    addResult('toggle-results', '✅ تم تشغيل toggleSection بنجاح');
                } else {
                    addResult('toggle-results', '❌ لم يتم العثور على عنصر الاختبار', true);
                }
            } catch (error) {
                addResult('toggle-results', `❌ خطأ: ${error.message}`, true);
            }
        }

        function testSectionManager() {
            const container = document.getElementById('toggle-results');
            
            try {
                if (window.sectionManager) {
                    addResult('toggle-results', '✅ sectionManager موجود');
                    
                    if (typeof window.sectionManager.toggle === 'function') {
                        addResult('toggle-results', '✅ وظيفة sectionManager.toggle موجودة');
                    } else {
                        addResult('toggle-results', '❌ وظيفة sectionManager.toggle غير موجودة', true);
                    }
                } else {
                    addResult('toggle-results', '❌ sectionManager غير موجود', true);
                }
            } catch (error) {
                addResult('toggle-results', `❌ خطأ في sectionManager: ${error.message}`, true);
            }
        }

        function testSmartSearch() {
            const container = document.getElementById('component-results');
            container.innerHTML = '';
            
            try {
                if (window.smartSearch) {
                    addResult('component-results', '✅ البحث الذكي محمل');
                } else {
                    addResult('component-results', '❌ البحث الذكي غير محمل', true);
                }
            } catch (error) {
                addResult('component-results', `❌ خطأ في البحث الذكي: ${error.message}`, true);
            }
        }

        function testAIAssistant() {
            const container = document.getElementById('component-results');
            
            try {
                if (window.aiAssistant) {
                    addResult('component-results', '✅ المساعد الذكي محمل');
                } else {
                    addResult('component-results', '❌ المساعد الذكي غير محمل', true);
                }
            } catch (error) {
                addResult('component-results', `❌ خطأ في المساعد الذكي: ${error.message}`, true);
            }
        }

        function testRatingSystem() {
            const container = document.getElementById('component-results');
            
            try {
                if (window.ratingSystem) {
                    addResult('component-results', '✅ نظام التقييم محمل');
                } else {
                    addResult('component-results', '❌ نظام التقييم غير محمل', true);
                }
            } catch (error) {
                addResult('component-results', `❌ خطأ في نظام التقييم: ${error.message}`, true);
            }
        }

        function testAnalytics() {
            const container = document.getElementById('component-results');
            
            try {
                if (window.analyticsDashboard) {
                    addResult('component-results', '✅ لوحة التحكم التحليلية محملة');
                } else {
                    addResult('component-results', '❌ لوحة التحكم التحليلية غير محملة', true);
                }
            } catch (error) {
                addResult('component-results', `❌ خطأ في لوحة التحكم: ${error.message}`, true);
            }
        }

        // تشغيل الاختبارات تلقائياً عند التحميل
        window.addEventListener('load', () => {
            console.log('🧪 بدء اختبارات الموقع...');
            
            setTimeout(() => {
                testSectionManager();
                testSmartSearch();
                testAIAssistant();
                testRatingSystem();
                testAnalytics();
            }, 1000);
        });

        // وظيفة toggleSection للاختبار
        function toggleSection(header) {
            console.log('🔄 تشغيل toggleSection:', header);
            
            if (window.sectionManager && window.sectionManager.toggle) {
                window.sectionManager.toggle(header);
            } else {
                // وظيفة بديلة بسيطة
                const section = header.parentElement;
                const content = section.querySelector('.content');
                const icon = header.querySelector('.toggle-icon');
                
                if (content.style.display === 'none' || section.classList.contains('collapsed')) {
                    content.style.display = 'block';
                    section.classList.remove('collapsed');
                    icon.textContent = '▼';
                } else {
                    content.style.display = 'none';
                    section.classList.add('collapsed');
                    icon.textContent = '▶';
                }
            }
        }
    </script>
</body>
</html>
