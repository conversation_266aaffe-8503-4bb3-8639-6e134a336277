// نظام عرض الأقسام المحسّن مع تتبع الموقع
class EnhancedSectionsManager {
    constructor() {
        this.currentSection = null;
        this.sectionsData = [];
        this.observer = null;
        this.tocContainer = null;
        this.progressBar = null;
        
        this.init();
    }

    async init() {
        this.collectSectionsData();
        this.createTableOfContents();

        this.enhanceSectionDisplay();
        this.setupIntersectionObserver();

        console.log('📋 نظام الأقسام المحسّن جاهز');
    }

    // جمع بيانات الأقسام
    collectSectionsData() {
        const sections = document.querySelectorAll('section[id]');
        this.sectionsData = Array.from(sections).map((section, index) => {
            const title = section.querySelector('h2')?.textContent?.trim() || `قسم ${index + 1}`;
            const content = section.querySelector('.content');
            const category = section.getAttribute('data-category') || 'عام';
            const complexity = section.getAttribute('data-complexity') || 'متوسط';
            
            return {
                id: section.id,
                title: title.replace(/^\d+\.\s*/, ''), // إزالة الرقم من البداية
                fullTitle: title,
                element: section,
                content: content,
                category: category,
                complexity: complexity,
                index: index + 1,
                isVisible: false,
                readingProgress: 0
            };
        });
    }

    // إنشاء جدول المحتويات المحسّن
    createTableOfContents() {
        const tocHTML = `
            <div id="enhanced-toc" class="enhanced-toc">
                <div class="toc-header">
                    <h3>📋 فهرس المحتويات</h3>
                    <div class="toc-controls">
                        <button class="toc-toggle-all" id="expandAll">توسيع الكل</button>
                        <button class="toc-toggle-all" id="collapseAll">طي الكل</button>
                        <button class="toc-filter" id="filterToc">🔍</button>
                    </div>
                </div>
                <div class="toc-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="readingProgress"></div>
                    </div>
                    <span class="progress-text" id="progressText">0% مكتمل</span>
                </div>
                <div class="toc-content" id="tocContent">
                    ${this.generateTocItems()}
                </div>
            </div>
        `;

        // إدراج جدول المحتويات بعد الشريط العلوي
        const mainNav = document.querySelector('.main-nav');
        if (mainNav) {
            mainNav.insertAdjacentHTML('afterend', tocHTML);
            this.tocContainer = document.getElementById('enhanced-toc');
            this.setupTocEvents();
        }
    }

    // توليد عناصر جدول المحتويات
    generateTocItems() {
        const categories = this.groupByCategory();
        let html = '';

        Object.entries(categories).forEach(([category, sections]) => {
            html += `
                <div class="toc-category">
                    <div class="category-header" data-category="${category}">
                        <span class="category-icon">${this.getCategoryIcon(category)}</span>
                        <span class="category-name">${this.getCategoryName(category)}</span>
                        <span class="category-count">(${sections.length})</span>
                        <span class="category-toggle">▼</span>
                    </div>
                    <div class="category-sections">
                        ${sections.map(section => this.generateTocItem(section)).join('')}
                    </div>
                </div>
            `;
        });

        return html;
    }

    // تجميع الأقسام حسب الفئة
    groupByCategory() {
        const categories = {};
        this.sectionsData.forEach(section => {
            const category = section.category || 'عام';
            if (!categories[category]) {
                categories[category] = [];
            }
            categories[category].push(section);
        });
        return categories;
    }

    // توليد عنصر جدول المحتويات
    generateTocItem(section) {
        const complexityClass = this.getComplexityClass(section.complexity);
        const estimatedTime = this.estimateReadingTime(section.content?.textContent || '');
        
        return `
            <div class="toc-item ${complexityClass}" data-section="${section.id}">
                <div class="toc-item-header">
                    <span class="section-number">${section.index}</span>
                    <span class="section-title">${section.title}</span>
                    <div class="section-meta">
                        <span class="reading-time">${estimatedTime} دقيقة</span>
                        <span class="complexity-badge ${complexityClass}">${this.getComplexityText(section.complexity)}</span>
                    </div>
                </div>
                <div class="toc-item-progress">
                    <div class="item-progress-bar">
                        <div class="item-progress-fill" data-section="${section.id}"></div>
                    </div>
                    <span class="item-status">غير مقروء</span>
                </div>
            </div>
        `;
    }



    // تحسين عرض الأقسام
    enhanceSectionDisplay() {
        this.sectionsData.forEach(section => {
            this.enhanceSingleSection(section);
        });
    }

    // تحسين قسم واحد
    enhanceSingleSection(sectionData) {
        const section = sectionData.element;
        const header = section.querySelector('h2');
        const content = section.querySelector('.content');

        if (!header || !content) return;

        // إنشاء هيكل محسّن للقسم
        const enhancedHTML = `
            <div class="section-wrapper">
                <div class="section-header-enhanced">
                    <div class="section-meta-info">
                        <span class="section-number-badge">${sectionData.index}</span>
                        <div class="section-details">
                            <h2 class="section-title-enhanced">${sectionData.title}</h2>
                            <div class="section-badges">
                                <span class="category-badge">${this.getCategoryName(sectionData.category)}</span>
                                <span class="complexity-badge ${this.getComplexityClass(sectionData.complexity)}">
                                    ${this.getComplexityText(sectionData.complexity)}
                                </span>
                                <span class="reading-time-badge">
                                    ⏱️ ${this.estimateReadingTime(content.textContent)} دقيقة
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="section-controls">
                        <button class="section-bookmark" data-section="${sectionData.id}" title="إضافة للمفضلة">
                            ⭐
                        </button>
                        <button class="section-share" data-section="${sectionData.id}" title="مشاركة">
                            🔗
                        </button>
                        <button class="section-toggle-btn" data-section="${sectionData.id}">
                            <span class="toggle-text">عرض المحتوى</span>
                            <span class="toggle-icon">▼</span>
                        </button>
                    </div>
                </div>
                <div class="section-content-wrapper" data-section="${sectionData.id}">
                    <div class="content-progress-indicator">
                        <div class="content-progress-bar">
                            <div class="content-progress-fill" data-section="${sectionData.id}"></div>
                        </div>
                    </div>
                    <div class="section-content-enhanced">
                        ${content.innerHTML}
                    </div>
                </div>
            </div>
        `;

        // استبدال المحتوى القديم
        section.innerHTML = enhancedHTML;
        
        // إضافة كلاسات للتحكم في العرض
        section.classList.add('enhanced-section');
        if (sectionData.index > 1) {
            section.classList.add('collapsed');
        }

        // ربط الأحداث
        this.bindSectionEvents(section, sectionData);
    }

    // ربط أحداث القسم
    bindSectionEvents(section, sectionData) {
        const toggleBtn = section.querySelector('.section-toggle-btn');
        const bookmarkBtn = section.querySelector('.section-bookmark');
        const shareBtn = section.querySelector('.section-share');
        const contentWrapper = section.querySelector('.section-content-wrapper');

        // زر التبديل
        if (toggleBtn && contentWrapper) {
            toggleBtn.addEventListener('click', () => {
                this.toggleSection(section, sectionData);
            });
        }

        // زر المفضلة
        if (bookmarkBtn) {
            bookmarkBtn.addEventListener('click', () => {
                this.toggleBookmark(sectionData.id);
            });
        }

        // زر المشاركة
        if (shareBtn) {
            shareBtn.addEventListener('click', () => {
                this.shareSection(sectionData);
            });
        }
    }

    // تبديل حالة القسم
    toggleSection(section, sectionData) {
        const contentWrapper = section.querySelector('.section-content-wrapper');
        const toggleBtn = section.querySelector('.section-toggle-btn');
        const toggleText = toggleBtn.querySelector('.toggle-text');
        const toggleIcon = toggleBtn.querySelector('.toggle-icon');

        const isCollapsed = section.classList.contains('collapsed');

        if (isCollapsed) {
            // فتح القسم
            section.classList.remove('collapsed');
            contentWrapper.style.maxHeight = contentWrapper.scrollHeight + 'px';
            toggleText.textContent = 'إخفاء المحتوى';
            toggleIcon.textContent = '▲';
            
            // تمرير سلس للقسم
            setTimeout(() => {
                section.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }, 100);
            
        } else {
            // إغلاق القسم
            section.classList.add('collapsed');
            contentWrapper.style.maxHeight = '0';
            toggleText.textContent = 'عرض المحتوى';
            toggleIcon.textContent = '▼';
        }

        // حفظ الحالة
        localStorage.setItem(`section_${sectionData.id}`, isCollapsed ? 'expanded' : 'collapsed');
        
        // تحديث جدول المحتويات
        this.updateTocItemStatus(sectionData.id, !isCollapsed);
    }

    // إعداد مراقب التقاطع
    setupIntersectionObserver() {
        const options = {
            root: null,
            rootMargin: '-20% 0px -20% 0px',
            threshold: [0, 0.25, 0.5, 0.75, 1]
        };

        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const sectionId = entry.target.id;
                const sectionData = this.sectionsData.find(s => s.id === sectionId);
                
                if (sectionData) {
                    sectionData.isVisible = entry.isIntersecting;
                    sectionData.readingProgress = entry.intersectionRatio;
                    
                    if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                        this.setCurrentSection(sectionData);
                    }
                    
                    this.updateProgress();
                }
            });
        }, options);

        // مراقبة جميع الأقسام
        this.sectionsData.forEach(section => {
            this.observer.observe(section.element);
        });
    }

    // تعيين القسم الحالي
    setCurrentSection(sectionData) {
        if (this.currentSection !== sectionData.id) {
            this.currentSection = sectionData.id;

            this.updateTocHighlight(sectionData.id);
        }
    }



    // تحديث التقدم
    updateProgress() {
        const totalSections = this.sectionsData.length;
        const visibleSections = this.sectionsData.filter(s => s.readingProgress > 0.5).length;
        const progressPercentage = (visibleSections / totalSections) * 100;

        // تحديث شريط التقدم الرئيسي
        const progressFill = document.getElementById('readingProgress');
        const progressText = document.getElementById('progressText');

        if (progressFill) {
            progressFill.style.width = `${progressPercentage}%`;
        }

        if (progressText) {
            progressText.textContent = `${Math.round(progressPercentage)}% مكتمل`;
        }

        // تحديث تقدم الأقسام الفردية
        this.sectionsData.forEach(section => {
            const itemProgress = document.querySelector(`[data-section="${section.id}"] .item-progress-fill`);
            if (itemProgress) {
                itemProgress.style.width = `${section.readingProgress * 100}%`;
            }
        });
    }

    // إعداد أحداث جدول المحتويات
    setupTocEvents() {
        // أزرار التوسيع/الطي
        document.getElementById('expandAll')?.addEventListener('click', () => {
            this.sectionsData.forEach(section => {
                if (section.element.classList.contains('collapsed')) {
                    this.toggleSection(section.element, section);
                }
            });
        });

        document.getElementById('collapseAll')?.addEventListener('click', () => {
            this.sectionsData.forEach(section => {
                if (!section.element.classList.contains('collapsed')) {
                    this.toggleSection(section.element, section);
                }
            });
        });

        // النقر على عناصر جدول المحتويات
        document.querySelectorAll('.toc-item').forEach(item => {
            item.addEventListener('click', () => {
                const sectionId = item.getAttribute('data-section');
                this.scrollToSection(sectionId);
            });
        });

        // تبديل الفئات
        document.querySelectorAll('.category-header').forEach(header => {
            header.addEventListener('click', () => {
                const category = header.parentElement;
                category.classList.toggle('collapsed');
            });
        });
    }



    // التنقل بين الأقسام
    navigateToSection(direction) {
        const currentIndex = this.sectionsData.findIndex(s => s.id === this.currentSection);
        let targetIndex;

        if (direction === 'prev') {
            targetIndex = Math.max(0, currentIndex - 1);
        } else {
            targetIndex = Math.min(this.sectionsData.length - 1, currentIndex + 1);
        }

        const targetSection = this.sectionsData[targetIndex];
        if (targetSection) {
            this.scrollToSection(targetSection.id);
        }
    }

    // التمرير إلى قسم
    scrollToSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    // وظائف مساعدة
    getCategoryIcon(category) {
        const icons = {
            'basics': '🎯',
            'analysis': '📊',
            'comparison': '⚖️',
            'tools': '🛠️',
            'advanced': '🚀',
            'summary': '📋'
        };
        return icons[category] || '📄';
    }

    getCategoryName(category) {
        const names = {
            'basics': 'الأساسيات',
            'analysis': 'التحليل',
            'comparison': 'المقارنة',
            'tools': 'الأدوات',
            'advanced': 'متقدم',
            'summary': 'الملخص'
        };
        return names[category] || 'عام';
    }

    getComplexityClass(complexity) {
        return `complexity-${complexity}`;
    }

    getComplexityText(complexity) {
        const texts = {
            'beginner': 'مبتدئ',
            'intermediate': 'متوسط',
            'advanced': 'متقدم'
        };
        return texts[complexity] || 'متوسط';
    }

    estimateReadingTime(text) {
        const wordsPerMinute = 200;
        const words = text.split(/\s+/).length;
        return Math.max(1, Math.round(words / wordsPerMinute));
    }

    // إضافة/إزالة من المفضلة
    toggleBookmark(sectionId) {
        const bookmarks = JSON.parse(localStorage.getItem('bookmarkedSections') || '[]');
        const index = bookmarks.indexOf(sectionId);
        
        if (index > -1) {
            bookmarks.splice(index, 1);
        } else {
            bookmarks.push(sectionId);
        }
        
        localStorage.setItem('bookmarkedSections', JSON.stringify(bookmarks));
        this.updateBookmarkButton(sectionId, index === -1);
    }

    // تحديث زر المفضلة
    updateBookmarkButton(sectionId, isBookmarked) {
        const btn = document.querySelector(`[data-section="${sectionId}"] .section-bookmark`);
        if (btn) {
            btn.textContent = isBookmarked ? '⭐' : '☆';
            btn.title = isBookmarked ? 'إزالة من المفضلة' : 'إضافة للمفضلة';
        }
    }

    // مشاركة القسم
    shareSection(sectionData) {
        const url = `${window.location.origin}${window.location.pathname}#${sectionData.id}`;
        
        if (navigator.share) {
            navigator.share({
                title: sectionData.title,
                text: `اطلع على هذا القسم: ${sectionData.title}`,
                url: url
            });
        } else {
            navigator.clipboard.writeText(url).then(() => {
                if (window.enhancedNavManager) {
                    window.enhancedNavManager.showNotification('تم نسخ الرابط', 'success');
                }
            });
        }
    }

    // تحديث حالة عنصر جدول المحتويات
    updateTocItemStatus(sectionId, isExpanded) {
        const tocItem = document.querySelector(`[data-section="${sectionId}"].toc-item`);
        if (tocItem) {
            const status = tocItem.querySelector('.item-status');
            if (status) {
                status.textContent = isExpanded ? 'مفتوح' : 'مطوي';
            }
        }
    }

    // تحديث تمييز جدول المحتويات
    updateTocHighlight(sectionId) {
        // إزالة التمييز السابق
        document.querySelectorAll('.toc-item.active').forEach(item => {
            item.classList.remove('active');
        });
        
        // إضافة التمييز الجديد
        const activeItem = document.querySelector(`[data-section="${sectionId}"].toc-item`);
        if (activeItem) {
            activeItem.classList.add('active');
        }
    }




}

// تهيئة نظام الأقسام المحسّن
const enhancedSections = new EnhancedSectionsManager();

// تصدير للاستخدام العام
window.enhancedSections = enhancedSections;
