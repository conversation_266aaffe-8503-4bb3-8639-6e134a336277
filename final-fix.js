// الإصلاح النهائي الشامل لجميع وظائف الموقع
console.log('🔧 بدء الإصلاح النهائي الشامل...');

// انتظار تحميل DOM
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM جاهز - بدء الإصلاحات');
    
    // إصلاح 1: وظيفة toggleSection
    setTimeout(() => {
        console.log('🔧 إصلاح وظيفة toggleSection...');
        
        // إنشاء وظيفة toggleSection محسّنة
        window.toggleSection = function(headerElement) {
            console.log('🔄 تشغيل toggleSection:', headerElement);
            
            try {
                let section, content, icon;
                
                if (headerElement.tagName === 'H2') {
                    section = headerElement.parentElement;
                    content = section.querySelector('.content');
                    icon = headerElement.querySelector('.toggle-icon');
                } else {
                    section = headerElement.closest('section');
                    content = section.querySelector('.content');
                    icon = section.querySelector('.toggle-icon');
                }
                
                if (!section || !content || !icon) {
                    console.error('❌ لم يتم العثور على العناصر المطلوبة');
                    return;
                }
                
                const isCollapsed = section.classList.contains('collapsed') || 
                                   content.style.display === 'none' ||
                                   getComputedStyle(content).display === 'none';
                
                if (isCollapsed) {
                    section.classList.remove('collapsed');
                    section.classList.add('expanded');
                    content.style.display = 'block';
                    icon.textContent = '▼';
                    console.log('✅ تم فتح القسم');
                } else {
                    section.classList.add('collapsed');
                    section.classList.remove('expanded');
                    content.style.display = 'none';
                    icon.textContent = '▶';
                    console.log('✅ تم إغلاق القسم');
                }
                
                // حفظ الحالة
                const sectionId = section.id;
                if (sectionId) {
                    const newState = section.classList.contains('expanded') ? 'expanded' : 'collapsed';
                    localStorage.setItem(sectionId, newState);
                }
                
            } catch (error) {
                console.error('❌ خطأ في toggleSection:', error);
            }
        };
        
        // ربط الأحداث للعناوين
        document.querySelectorAll('section h2').forEach(header => {
            header.removeAttribute('onclick');
            header.style.cursor = 'pointer';
            header.style.userSelect = 'none';
            
            header.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                window.toggleSection(this);
            });
        });
        
        console.log('✅ تم إصلاح وظيفة toggleSection');
    }, 500);
    
    // إصلاح 2: لوحة التحكم التحليلية
    setTimeout(() => {
        console.log('🔧 إصلاح لوحة التحكم التحليلية...');
        
        const dashboardToggle = document.getElementById('dashboardToggle');
        const dashboard = document.getElementById('analyticsDashboard');
        
        if (dashboardToggle && dashboard) {
            console.log('✅ تم العثور على عناصر لوحة التحكم');
            
            // إزالة الأحداث القديمة
            dashboardToggle.onclick = null;
            
            // إضافة حدث جديد
            dashboardToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔄 النقر على زر لوحة التحكم');
                
                const isOpen = dashboard.classList.contains('open');
                
                if (isOpen) {
                    dashboard.classList.remove('open');
                    dashboard.style.right = '-100%';
                    console.log('❌ إغلاق لوحة التحكم');
                } else {
                    dashboard.classList.add('open');
                    dashboard.style.right = '0';
                    console.log('✅ فتح لوحة التحكم');
                }
            });
            
            // إضافة حدث للزر إغلاق
            const closeBtn = document.getElementById('closeDashboard');
            if (closeBtn) {
                closeBtn.addEventListener('click', function() {
                    dashboard.classList.remove('open');
                    dashboard.style.right = '-100%';
                });
            }
            
            console.log('✅ تم إصلاح لوحة التحكم التحليلية');
        } else {
            console.warn('⚠️ لم يتم العثور على عناصر لوحة التحكم');
        }
    }, 1000);
    
    // إصلاح 3: المساعد الذكي
    setTimeout(() => {
        console.log('🔧 إصلاح المساعد الذكي...');
        
        const assistantToggle = document.getElementById('assistantToggle');
        const assistant = document.getElementById('ai-assistant');
        
        if (assistantToggle && assistant) {
            assistantToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔄 النقر على زر المساعد الذكي');
                
                const isOpen = assistant.classList.contains('open');
                
                if (isOpen) {
                    assistant.classList.remove('open');
                    console.log('❌ إغلاق المساعد الذكي');
                } else {
                    assistant.classList.add('open');
                    console.log('✅ فتح المساعد الذكي');
                }
            });
            
            console.log('✅ تم إصلاح المساعد الذكي');
        }
    }, 1500);
    
    // إصلاح 4: استرجاع حالة الأقسام
    setTimeout(() => {
        console.log('🔧 استرجاع حالة الأقسام...');
        
        document.querySelectorAll('section').forEach(section => {
            const sectionId = section.id;
            const savedState = localStorage.getItem(sectionId);
            const content = section.querySelector('.content');
            const icon = section.querySelector('.toggle-icon');
            
            if (content && icon) {
                if (savedState === 'expanded') {
                    section.classList.add('expanded');
                    section.classList.remove('collapsed');
                    content.style.display = 'block';
                    icon.textContent = '▼';
                } else {
                    section.classList.remove('expanded');
                    section.classList.add('collapsed');
                    content.style.display = 'none';
                    icon.textContent = '▶';
                }
            }
        });
        
        console.log('✅ تم استرجاع حالة الأقسام');
    }, 2000);
    
    // إصلاح 5: الاختصارات
    document.addEventListener('keydown', function(e) {
        // Alt + D لفتح لوحة التحكم
        if (e.altKey && e.key === 'd') {
            e.preventDefault();
            const dashboardToggle = document.getElementById('dashboardToggle');
            if (dashboardToggle) {
                dashboardToggle.click();
            }
        }
        
        // Alt + A لفتح المساعد الذكي
        if (e.altKey && e.key === 'a') {
            e.preventDefault();
            const assistantToggle = document.getElementById('assistantToggle');
            if (assistantToggle) {
                assistantToggle.click();
            }
        }
        
        // Escape لإغلاق النوافذ
        if (e.key === 'Escape') {
            const dashboard = document.getElementById('analyticsDashboard');
            const assistant = document.getElementById('ai-assistant');
            
            if (dashboard && dashboard.classList.contains('open')) {
                dashboard.classList.remove('open');
                dashboard.style.right = '-100%';
            }
            
            if (assistant && assistant.classList.contains('open')) {
                assistant.classList.remove('open');
            }
        }
    });
    
    console.log('✅ تم تفعيل الاختصارات');
});

// إضافة أنماط CSS محسّنة
const style = document.createElement('style');
style.textContent = `
    /* إصلاح الأقسام */
    section.collapsed .content {
        display: none !important;
    }
    
    section.expanded .content {
        display: block !important;
    }
    
    section h2 {
        cursor: pointer !important;
        user-select: none !important;
        transition: background-color 0.3s ease !important;
    }
    
    section h2:hover {
        background-color: #f0f0f0 !important;
    }
    
    .toggle-icon {
        transition: transform 0.3s ease !important;
        float: left !important;
        margin-left: 10px !important;
    }
    
    /* إصلاح لوحة التحكم */
    .analytics-dashboard {
        position: fixed !important;
        top: 0 !important;
        right: -100% !important;
        width: 400px !important;
        height: 100vh !important;
        background: white !important;
        z-index: 10002 !important;
        transition: right 0.3s ease !important;
        overflow-y: auto !important;
        box-shadow: -5px 0 20px rgba(0,0,0,0.1) !important;
        border-left: 1px solid #ddd !important;
    }
    
    .analytics-dashboard.open {
        right: 0 !important;
    }
    
    .dashboard-toggle {
        position: fixed !important;
        top: 50% !important;
        right: 20px !important;
        transform: translateY(-50%) !important;
        width: 50px !important;
        height: 50px !important;
        background: linear-gradient(135deg, #4535cd, #6c5ce7) !important;
        border: none !important;
        border-radius: 50% !important;
        color: white !important;
        font-size: 20px !important;
        cursor: pointer !important;
        z-index: 9998 !important;
        box-shadow: 0 4px 15px rgba(69, 53, 205, 0.3) !important;
        transition: all 0.3s ease !important;
    }
    
    .dashboard-toggle:hover {
        transform: translateY(-50%) scale(1.1) !important;
        box-shadow: 0 6px 20px rgba(69, 53, 205, 0.4) !important;
    }
    
    /* إصلاح المساعد الذكي */
    .ai-assistant {
        position: fixed !important;
        bottom: 20px !important;
        left: 20px !important;
        width: 350px !important;
        height: 500px !important;
        background: white !important;
        border-radius: 15px !important;
        box-shadow: 0 10px 40px rgba(0,0,0,0.2) !important;
        z-index: 10000 !important;
        transform: translateY(100vh) !important;
        transition: transform 0.3s ease !important;
        border: 1px solid #ddd !important;
    }
    
    .ai-assistant.open {
        transform: translateY(0) !important;
    }
    
    .assistant-toggle {
        position: fixed !important;
        bottom: 20px !important;
        left: 20px !important;
        width: 60px !important;
        height: 60px !important;
        background: linear-gradient(135deg, #4535cd, #6c5ce7) !important;
        border: none !important;
        border-radius: 50% !important;
        color: white !important;
        font-size: 24px !important;
        cursor: pointer !important;
        box-shadow: 0 4px 20px rgba(69, 53, 205, 0.3) !important;
        transition: all 0.3s ease !important;
        z-index: 9999 !important;
    }
    
    .assistant-toggle:hover {
        transform: scale(1.1) !important;
        box-shadow: 0 6px 25px rgba(69, 53, 205, 0.4) !important;
    }
    
    /* تحسينات للأجهزة المحمولة */
    @media (max-width: 768px) {
        .analytics-dashboard {
            width: 100% !important;
        }
        
        .dashboard-toggle {
            right: 15px !important;
            width: 45px !important;
            height: 45px !important;
            font-size: 18px !important;
        }
        
        .ai-assistant {
            width: 100% !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            height: 70vh !important;
            border-radius: 15px 15px 0 0 !important;
        }
        
        .assistant-toggle {
            bottom: 15px !important;
            left: 15px !important;
            width: 50px !important;
            height: 50px !important;
        }
    }
`;
document.head.appendChild(style);

console.log('🎨 تم إضافة الأنماط المحسّنة');
console.log('✅ تم الانتهاء من الإصلاح النهائي الشامل');

// تقرير حالة النظام
setTimeout(() => {
    console.log('📊 تقرير حالة النظام:');
    console.log('   toggleSection:', typeof window.toggleSection === 'function' ? '✅' : '❌');
    console.log('   لوحة التحكم:', document.getElementById('analyticsDashboard') ? '✅' : '❌');
    console.log('   زر لوحة التحكم:', document.getElementById('dashboardToggle') ? '✅' : '❌');
    console.log('   المساعد الذكي:', document.getElementById('ai-assistant') ? '✅' : '❌');
    console.log('   زر المساعد:', document.getElementById('assistantToggle') ? '✅' : '❌');
    console.log('🎉 النظام جاهز للاستخدام!');
}, 3000);
