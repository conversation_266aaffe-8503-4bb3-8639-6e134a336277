// إصلاح سريع لمشكلة toggleSection
console.log('🔧 بدء الإصلاح السريع...');

// التأكد من وجود DOM
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM جاهز');
    
    // إصلاح وظيفة toggleSection
    window.toggleSection = function(headerElement) {
        console.log('🔄 تشغيل toggleSection:', headerElement);
        
        try {
            // العثور على العناصر المطلوبة
            let section, content, icon;
            
            if (headerElement.tagName === 'H2') {
                section = headerElement.parentElement;
                content = section.querySelector('.content');
                icon = headerElement.querySelector('.toggle-icon');
            } else {
                section = headerElement.closest('section');
                content = section.querySelector('.content');
                icon = section.querySelector('.toggle-icon');
            }
            
            if (!section || !content || !icon) {
                console.error('❌ لم يتم العثور على العناصر المطلوبة');
                return;
            }
            
            // تحديد الحالة الحالية
            const isCollapsed = section.classList.contains('collapsed') || 
                               content.style.display === 'none' ||
                               getComputedStyle(content).display === 'none';
            
            console.log('📊 حالة القسم:', isCollapsed ? 'مطوي' : 'مفتوح');
            
            if (isCollapsed) {
                // فتح القسم
                section.classList.remove('collapsed');
                section.classList.add('expanded');
                content.style.display = 'block';
                icon.textContent = '▼';
                console.log('✅ تم فتح القسم');
            } else {
                // إغلاق القسم
                section.classList.add('collapsed');
                section.classList.remove('expanded');
                content.style.display = 'none';
                icon.textContent = '▶';
                console.log('✅ تم إغلاق القسم');
            }
            
            // حفظ الحالة
            const sectionId = section.id;
            if (sectionId) {
                const newState = section.classList.contains('expanded') ? 'expanded' : 'collapsed';
                localStorage.setItem(sectionId, newState);
                console.log('💾 تم حفظ حالة القسم:', sectionId, newState);
            }
            
        } catch (error) {
            console.error('❌ خطأ في toggleSection:', error);
        }
    };
    
    // إصلاح الأحداث المربوطة
    document.querySelectorAll('section h2').forEach(header => {
        // إزالة الأحداث القديمة
        header.removeAttribute('onclick');
        
        // إضافة حدث جديد
        header.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('👆 تم النقر على العنوان:', this);
            window.toggleSection(this);
        });
        
        console.log('🔗 تم ربط الحدث للعنوان:', header.textContent.trim().substring(0, 30) + '...');
    });
    
    // استرجاع حالة الأقسام المحفوظة
    document.querySelectorAll('section').forEach(section => {
        const sectionId = section.id;
        const savedState = localStorage.getItem(sectionId);
        const content = section.querySelector('.content');
        const icon = section.querySelector('.toggle-icon');
        
        if (content && icon) {
            if (savedState === 'expanded') {
                section.classList.add('expanded');
                section.classList.remove('collapsed');
                content.style.display = 'block';
                icon.textContent = '▼';
                console.log('🔄 استرجاع حالة مفتوحة للقسم:', sectionId);
            } else {
                section.classList.remove('expanded');
                section.classList.add('collapsed');
                content.style.display = 'none';
                icon.textContent = '▶';
                console.log('🔄 استرجاع حالة مطوية للقسم:', sectionId);
            }
        }
    });
    
    console.log('✅ تم الانتهاء من الإصلاح السريع');
});

// إضافة أنماط CSS للتأكد من عمل الطي/الفتح
const style = document.createElement('style');
style.textContent = `
    section.collapsed .content {
        display: none !important;
    }
    
    section.expanded .content {
        display: block !important;
    }
    
    section h2 {
        cursor: pointer;
        user-select: none;
    }
    
    section h2:hover {
        background-color: #f0f0f0;
    }
    
    .toggle-icon {
        transition: transform 0.3s ease;
    }
    
    section.expanded .toggle-icon {
        transform: rotate(90deg);
    }
`;
document.head.appendChild(style);

console.log('🎨 تم إضافة الأنماط المطلوبة');
