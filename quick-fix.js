// إصلاح سريع لمشكلة toggleSection
console.log('🔧 بدء الإصلاح السريع...');

// التأكد من وجود DOM
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM جاهز');
    
    // إصلاح وظيفة toggleSection
    window.toggleSection = function(headerElement) {
        console.log('🔄 تشغيل toggleSection:', headerElement);
        
        try {
            // العثور على العناصر المطلوبة
            let section, content, icon;
            
            if (headerElement.tagName === 'H2') {
                section = headerElement.parentElement;
                content = section.querySelector('.content');
                icon = headerElement.querySelector('.toggle-icon');
            } else {
                section = headerElement.closest('section');
                content = section.querySelector('.content');
                icon = section.querySelector('.toggle-icon');
            }
            
            if (!section || !content || !icon) {
                console.error('❌ لم يتم العثور على العناصر المطلوبة');
                return;
            }
            
            // تحديد الحالة الحالية
            const isCollapsed = section.classList.contains('collapsed') || 
                               content.style.display === 'none' ||
                               getComputedStyle(content).display === 'none';
            
            console.log('📊 حالة القسم:', isCollapsed ? 'مطوي' : 'مفتوح');
            
            if (isCollapsed) {
                // فتح القسم
                section.classList.remove('collapsed');
                section.classList.add('expanded');
                content.style.display = 'block';
                icon.textContent = '▼';
                console.log('✅ تم فتح القسم');
            } else {
                // إغلاق القسم
                section.classList.add('collapsed');
                section.classList.remove('expanded');
                content.style.display = 'none';
                icon.textContent = '▶';
                console.log('✅ تم إغلاق القسم');
            }
            
            // حفظ الحالة
            const sectionId = section.id;
            if (sectionId) {
                const newState = section.classList.contains('expanded') ? 'expanded' : 'collapsed';
                localStorage.setItem(sectionId, newState);
                console.log('💾 تم حفظ حالة القسم:', sectionId, newState);
            }
            
        } catch (error) {
            console.error('❌ خطأ في toggleSection:', error);
        }
    };
    
    // إصلاح الأحداث المربوطة
    document.querySelectorAll('section h2').forEach(header => {
        // إزالة الأحداث القديمة
        header.removeAttribute('onclick');
        
        // إضافة حدث جديد
        header.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('👆 تم النقر على العنوان:', this);
            window.toggleSection(this);
        });
        
        console.log('🔗 تم ربط الحدث للعنوان:', header.textContent.trim().substring(0, 30) + '...');
    });
    
    // استرجاع حالة الأقسام المحفوظة
    document.querySelectorAll('section').forEach(section => {
        const sectionId = section.id;
        const savedState = localStorage.getItem(sectionId);
        const content = section.querySelector('.content');
        const icon = section.querySelector('.toggle-icon');
        
        if (content && icon) {
            if (savedState === 'expanded') {
                section.classList.add('expanded');
                section.classList.remove('collapsed');
                content.style.display = 'block';
                icon.textContent = '▼';
                console.log('🔄 استرجاع حالة مفتوحة للقسم:', sectionId);
            } else {
                section.classList.remove('expanded');
                section.classList.add('collapsed');
                content.style.display = 'none';
                icon.textContent = '▶';
                console.log('🔄 استرجاع حالة مطوية للقسم:', sectionId);
            }
        }
    });
    
    // إصلاح لوحة التحكم التحليلية
    setTimeout(() => {
        const dashboardToggle = document.getElementById('dashboardToggle');
        if (dashboardToggle) {
            console.log('🔧 إصلاح زر لوحة التحكم');

            // إزالة الأحداث القديمة
            dashboardToggle.removeEventListener('click', () => {});

            // إضافة حدث جديد
            dashboardToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔄 النقر على زر لوحة التحكم (من quick-fix)');

                if (window.analyticsDashboard && window.analyticsDashboard.toggleDashboard) {
                    window.analyticsDashboard.toggleDashboard();
                } else {
                    console.error('❌ analyticsDashboard غير متاح');

                    // إصلاح بديل
                    const dashboard = document.getElementById('analyticsDashboard');
                    if (dashboard) {
                        const isOpen = dashboard.classList.contains('open');
                        if (isOpen) {
                            dashboard.classList.remove('open');
                            dashboard.style.right = '-100%';
                        } else {
                            dashboard.classList.add('open');
                            dashboard.style.right = '0';
                        }
                    }
                }
            });

            console.log('✅ تم إصلاح زر لوحة التحكم');
        } else {
            console.warn('⚠️ لم يتم العثور على زر لوحة التحكم');
        }
    }, 2000);

    console.log('✅ تم الانتهاء من الإصلاح السريع');
});

// إضافة أنماط CSS للتأكد من عمل الطي/الفتح
const style = document.createElement('style');
style.textContent = `
    section.collapsed .content {
        display: none !important;
    }
    
    section.expanded .content {
        display: block !important;
    }
    
    section h2 {
        cursor: pointer;
        user-select: none;
    }
    
    section h2:hover {
        background-color: #f0f0f0;
    }
    
    .toggle-icon {
        transition: transform 0.3s ease;
    }

    section.expanded .toggle-icon {
        transform: rotate(90deg);
    }

    /* إصلاح لوحة التحكم التحليلية */
    .analytics-dashboard {
        position: fixed !important;
        top: 0 !important;
        right: -100% !important;
        width: 400px !important;
        height: 100vh !important;
        background: white !important;
        z-index: 10002 !important;
        transition: right 0.3s ease !important;
        overflow-y: auto !important;
        box-shadow: -5px 0 20px rgba(0,0,0,0.1) !important;
    }

    .analytics-dashboard.open {
        right: 0 !important;
    }

    .dashboard-toggle {
        position: fixed !important;
        top: 50% !important;
        right: 20px !important;
        transform: translateY(-50%) !important;
        width: 50px !important;
        height: 50px !important;
        background: linear-gradient(135deg, #4535cd, #6c5ce7) !important;
        border: none !important;
        border-radius: 50% !important;
        color: white !important;
        font-size: 20px !important;
        cursor: pointer !important;
        z-index: 9998 !important;
        box-shadow: 0 4px 15px rgba(69, 53, 205, 0.3) !important;
    }

    .dashboard-toggle:hover {
        transform: translateY(-50%) scale(1.1) !important;
        box-shadow: 0 6px 20px rgba(69, 53, 205, 0.4) !important;
    }

    @media (max-width: 768px) {
        .analytics-dashboard {
            width: 100% !important;
        }

        .dashboard-toggle {
            right: 15px !important;
            width: 45px !important;
            height: 45px !important;
            font-size: 18px !important;
        }
    }
`;
document.head.appendChild(style);

console.log('🎨 تم إضافة الأنماط المطلوبة');
