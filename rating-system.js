// نظام التقييم والمراجعة التفاعلي
class RatingSystem {
    constructor() {
        this.ratings = new Map();
        this.reviews = new Map();
        this.userRatings = new Map();
        
        this.init();
    }

    async init() {
        await this.loadRatings();
        this.createRatingUI();
        this.setupEventListeners();
        console.log('⭐ نظام التقييم جاهز');
    }

    // إنشاء واجهة التقييم
    createRatingUI() {
        const sections = document.querySelectorAll('section[data-category]');
        
        sections.forEach(section => {
            const ratingHTML = this.createRatingWidget(section.id);
            const content = section.querySelector('.content');
            if (content) {
                content.insertAdjacentHTML('beforeend', ratingHTML);
            }
        });

        // إضافة نافذة المراجعات
        this.createReviewModal();
    }

    // إنشاء أداة التقييم
    createRatingWidget(sectionId) {
        const currentRating = this.getRating(sectionId);
        const userRating = this.getUserRating(sectionId);
        
        return `
            <div class="rating-widget" data-section="${sectionId}">
                <div class="rating-header">
                    <h4>📊 تقييم المحتوى</h4>
                    <div class="rating-stats">
                        <span class="average-rating">${currentRating.average.toFixed(1)}</span>
                        <div class="stars-display">
                            ${this.generateStarsHTML(currentRating.average, 'display')}
                        </div>
                        <span class="rating-count">(${currentRating.count} تقييم)</span>
                    </div>
                </div>
                
                <div class="rating-actions">
                    <div class="user-rating">
                        <span>تقييمك:</span>
                        <div class="stars-input" data-section="${sectionId}">
                            ${this.generateStarsHTML(userRating, 'input')}
                        </div>
                    </div>
                    
                    <div class="rating-buttons">
                        <button class="rating-btn helpful" data-action="helpful" data-section="${sectionId}">
                            👍 مفيد (${currentRating.helpful || 0})
                        </button>
                        <button class="rating-btn not-helpful" data-action="not-helpful" data-section="${sectionId}">
                            👎 غير مفيد (${currentRating.notHelpful || 0})
                        </button>
                        <button class="rating-btn review" data-action="review" data-section="${sectionId}">
                            💬 كتابة مراجعة
                        </button>
                    </div>
                </div>
                
                <div class="rating-breakdown">
                    <div class="breakdown-item">
                        <span>الوضوح</span>
                        <div class="progress-bar">
                            <div class="progress" style="width: ${(currentRating.clarity || 0) * 20}%"></div>
                        </div>
                        <span>${(currentRating.clarity || 0).toFixed(1)}</span>
                    </div>
                    <div class="breakdown-item">
                        <span>الفائدة</span>
                        <div class="progress-bar">
                            <div class="progress" style="width: ${(currentRating.usefulness || 0) * 20}%"></div>
                        </div>
                        <span>${(currentRating.usefulness || 0).toFixed(1)}</span>
                    </div>
                    <div class="breakdown-item">
                        <span>الدقة</span>
                        <div class="progress-bar">
                            <div class="progress" style="width: ${(currentRating.accuracy || 0) * 20}%"></div>
                        </div>
                        <span>${(currentRating.accuracy || 0).toFixed(1)}</span>
                    </div>
                </div>
                
                <div class="recent-reviews" id="reviews-${sectionId}">
                    ${this.getRecentReviews(sectionId)}
                </div>
            </div>
        `;
    }

    // توليد نجوم HTML
    generateStarsHTML(rating, type) {
        let starsHTML = '';
        for (let i = 1; i <= 5; i++) {
            const filled = i <= rating;
            const starClass = type === 'input' ? 'star-input' : 'star-display';
            starsHTML += `
                <span class="${starClass} ${filled ? 'filled' : ''}" 
                      data-rating="${i}" 
                      ${type === 'input' ? 'title="' + i + ' نجوم"' : ''}>
                    ⭐
                </span>
            `;
        }
        return starsHTML;
    }

    // إنشاء نافذة المراجعات
    createReviewModal() {
        const modalHTML = `
            <div id="reviewModal" class="review-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>💬 كتابة مراجعة</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    
                    <div class="modal-body">
                        <div class="review-form">
                            <div class="rating-categories">
                                <div class="category-rating">
                                    <label>الوضوح:</label>
                                    <div class="stars-input" data-category="clarity">
                                        ${this.generateStarsHTML(0, 'input')}
                                    </div>
                                </div>
                                <div class="category-rating">
                                    <label>الفائدة:</label>
                                    <div class="stars-input" data-category="usefulness">
                                        ${this.generateStarsHTML(0, 'input')}
                                    </div>
                                </div>
                                <div class="category-rating">
                                    <label>الدقة:</label>
                                    <div class="stars-input" data-category="accuracy">
                                        ${this.generateStarsHTML(0, 'input')}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="review-text">
                                <label for="reviewComment">تعليقك:</label>
                                <textarea id="reviewComment" 
                                         placeholder="شاركنا رأيك في هذا المحتوى... ما الذي أعجبك؟ ما الذي يمكن تحسينه؟"
                                         rows="4"></textarea>
                            </div>
                            
                            <div class="review-tags">
                                <label>اختر العلامات المناسبة:</label>
                                <div class="tags-container">
                                    <button class="tag-btn" data-tag="مفيد">مفيد</button>
                                    <button class="tag-btn" data-tag="واضح">واضح</button>
                                    <button class="tag-btn" data-tag="شامل">شامل</button>
                                    <button class="tag-btn" data-tag="عملي">عملي</button>
                                    <button class="tag-btn" data-tag="محتاج تحسين">محتاج تحسين</button>
                                    <button class="tag-btn" data-tag="صعب الفهم">صعب الفهم</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button id="submitReview" class="submit-btn">إرسال المراجعة</button>
                        <button id="cancelReview" class="cancel-btn">إلغاء</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    // ربط الأحداث
    setupEventListeners() {
        // تقييم النجوم
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('star-input')) {
                this.handleStarRating(e.target);
            }
        });

        // أزرار التقييم
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('rating-btn')) {
                this.handleRatingAction(e.target);
            }
        });

        // نافذة المراجعة
        const modal = document.getElementById('reviewModal');
        const closeBtn = modal.querySelector('.modal-close');
        const cancelBtn = document.getElementById('cancelReview');
        const submitBtn = document.getElementById('submitReview');

        closeBtn.addEventListener('click', () => this.closeReviewModal());
        cancelBtn.addEventListener('click', () => this.closeReviewModal());
        submitBtn.addEventListener('click', () => this.submitReview());

        // العلامات
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tag-btn')) {
                e.target.classList.toggle('selected');
            }
        });

        // إغلاق النافذة بالنقر خارجها
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeReviewModal();
            }
        });
    }

    // معالجة تقييم النجوم
    handleStarRating(starElement) {
        const rating = parseInt(starElement.getAttribute('data-rating'));
        const container = starElement.parentElement;
        const sectionId = container.getAttribute('data-section');
        const category = container.getAttribute('data-category');

        // تحديث النجوم بصرياً
        const stars = container.querySelectorAll('.star-input');
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.add('filled');
            } else {
                star.classList.remove('filled');
            }
        });

        // حفظ التقييم
        if (sectionId) {
            this.saveUserRating(sectionId, rating);
            this.updateRatingDisplay(sectionId);
        }

        // إظهار رسالة شكر
        this.showThankYouMessage(container);
    }

    // معالجة إجراءات التقييم
    handleRatingAction(button) {
        const action = button.getAttribute('data-action');
        const sectionId = button.getAttribute('data-section');

        switch (action) {
            case 'helpful':
                this.markAsHelpful(sectionId);
                break;
            case 'not-helpful':
                this.markAsNotHelpful(sectionId);
                break;
            case 'review':
                this.openReviewModal(sectionId);
                break;
        }
    }

    // وضع علامة مفيد
    markAsHelpful(sectionId) {
        const rating = this.getRating(sectionId);
        rating.helpful = (rating.helpful || 0) + 1;
        this.saveRating(sectionId, rating);
        this.updateRatingDisplay(sectionId);
        
        this.showNotification('شكراً لك! تم تسجيل تقييمك.', 'success');
    }

    // وضع علامة غير مفيد
    markAsNotHelpful(sectionId) {
        const rating = this.getRating(sectionId);
        rating.notHelpful = (rating.notHelpful || 0) + 1;
        this.saveRating(sectionId, rating);
        this.updateRatingDisplay(sectionId);
        
        this.showNotification('شكراً لك! سنعمل على تحسين المحتوى.', 'info');
    }

    // فتح نافذة المراجعة
    openReviewModal(sectionId) {
        const modal = document.getElementById('reviewModal');
        modal.style.display = 'flex';
        modal.setAttribute('data-section', sectionId);
        
        // تحميل التقييم الحالي للمستخدم
        const userRating = this.getUserRating(sectionId);
        if (userRating > 0) {
            // تعبئة النجوم بالتقييم الحالي
            const categoryInputs = modal.querySelectorAll('.stars-input[data-category]');
            categoryInputs.forEach(input => {
                const stars = input.querySelectorAll('.star-input');
                stars.forEach((star, index) => {
                    if (index < userRating) {
                        star.classList.add('filled');
                    }
                });
            });
        }
    }

    // إغلاق نافذة المراجعة
    closeReviewModal() {
        const modal = document.getElementById('reviewModal');
        modal.style.display = 'none';
        
        // إعادة تعيين النموذج
        modal.querySelector('#reviewComment').value = '';
        modal.querySelectorAll('.star-input').forEach(star => {
            star.classList.remove('filled');
        });
        modal.querySelectorAll('.tag-btn').forEach(tag => {
            tag.classList.remove('selected');
        });
    }

    // إرسال المراجعة
    submitReview() {
        const modal = document.getElementById('reviewModal');
        const sectionId = modal.getAttribute('data-section');
        const comment = modal.querySelector('#reviewComment').value.trim();
        
        // جمع التقييمات
        const ratings = {};
        modal.querySelectorAll('.stars-input[data-category]').forEach(input => {
            const category = input.getAttribute('data-category');
            const filledStars = input.querySelectorAll('.star-input.filled').length;
            ratings[category] = filledStars;
        });

        // جمع العلامات
        const tags = Array.from(modal.querySelectorAll('.tag-btn.selected'))
            .map(tag => tag.getAttribute('data-tag'));

        if (comment.length < 10) {
            this.showNotification('يرجى كتابة تعليق أطول (على الأقل 10 أحرف)', 'warning');
            return;
        }

        // حفظ المراجعة
        const review = {
            sectionId,
            comment,
            ratings,
            tags,
            timestamp: new Date().toISOString(),
            userId: this.getUserId()
        };

        this.saveReview(review);
        this.updateRatingDisplay(sectionId);
        this.closeReviewModal();
        
        this.showNotification('شكراً لك! تم حفظ مراجعتك بنجاح.', 'success');
    }

    // الحصول على التقييم
    getRating(sectionId) {
        return this.ratings.get(sectionId) || {
            average: 0,
            count: 0,
            clarity: 0,
            usefulness: 0,
            accuracy: 0,
            helpful: 0,
            notHelpful: 0
        };
    }

    // حفظ التقييم
    saveRating(sectionId, rating) {
        this.ratings.set(sectionId, rating);
        localStorage.setItem('ratings', JSON.stringify(Array.from(this.ratings.entries())));
    }

    // حفظ تقييم المستخدم
    saveUserRating(sectionId, rating) {
        const userId = this.getUserId();
        const userKey = `${userId}-${sectionId}`;
        this.userRatings.set(userKey, rating);
        
        // تحديث المتوسط العام
        const currentRating = this.getRating(sectionId);
        currentRating.count += 1;
        currentRating.average = ((currentRating.average * (currentRating.count - 1)) + rating) / currentRating.count;
        
        this.saveRating(sectionId, currentRating);
        localStorage.setItem('userRatings', JSON.stringify(Array.from(this.userRatings.entries())));
    }

    // الحصول على تقييم المستخدم
    getUserRating(sectionId) {
        const userId = this.getUserId();
        const userKey = `${userId}-${sectionId}`;
        return this.userRatings.get(userKey) || 0;
    }

    // حفظ المراجعة
    saveReview(review) {
        const sectionReviews = this.reviews.get(review.sectionId) || [];
        sectionReviews.unshift(review);
        this.reviews.set(review.sectionId, sectionReviews.slice(0, 10)); // الاحتفاظ بآخر 10 مراجعات
        
        localStorage.setItem('reviews', JSON.stringify(Array.from(this.reviews.entries())));
        
        // تحديث عرض المراجعات
        this.updateReviewsDisplay(review.sectionId);
    }

    // الحصول على المراجعات الحديثة
    getRecentReviews(sectionId) {
        const reviews = this.reviews.get(sectionId) || [];
        if (reviews.length === 0) {
            return '<p class="no-reviews">لا توجد مراجعات بعد. كن أول من يكتب مراجعة!</p>';
        }

        return reviews.slice(0, 3).map(review => `
            <div class="review-item">
                <div class="review-header">
                    <span class="reviewer">👤 مستخدم</span>
                    <span class="review-date">${new Date(review.timestamp).toLocaleDateString('ar-SA')}</span>
                </div>
                <div class="review-content">
                    <p>${review.comment}</p>
                    ${review.tags.length > 0 ? `
                        <div class="review-tags">
                            ${review.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        `).join('');
    }

    // تحديث عرض التقييمات
    updateRatingDisplay(sectionId) {
        const widget = document.querySelector(`[data-section="${sectionId}"]`);
        if (!widget) return;

        const rating = this.getRating(sectionId);
        
        // تحديث المتوسط
        widget.querySelector('.average-rating').textContent = rating.average.toFixed(1);
        widget.querySelector('.rating-count').textContent = `(${rating.count} تقييم)`;
        
        // تحديث النجوم
        const starsDisplay = widget.querySelector('.stars-display');
        starsDisplay.innerHTML = this.generateStarsHTML(rating.average, 'display');
        
        // تحديث أشرطة التقدم
        widget.querySelector('.breakdown-item:nth-child(1) .progress').style.width = `${(rating.clarity || 0) * 20}%`;
        widget.querySelector('.breakdown-item:nth-child(2) .progress').style.width = `${(rating.usefulness || 0) * 20}%`;
        widget.querySelector('.breakdown-item:nth-child(3) .progress').style.width = `${(rating.accuracy || 0) * 20}%`;
        
        // تحديث أزرار مفيد/غير مفيد
        widget.querySelector('.helpful').textContent = `👍 مفيد (${rating.helpful || 0})`;
        widget.querySelector('.not-helpful').textContent = `👎 غير مفيد (${rating.notHelpful || 0})`;
    }

    // تحديث عرض المراجعات
    updateReviewsDisplay(sectionId) {
        const reviewsContainer = document.getElementById(`reviews-${sectionId}`);
        if (reviewsContainer) {
            reviewsContainer.innerHTML = this.getRecentReviews(sectionId);
        }
    }

    // إظهار رسالة شكر
    showThankYouMessage(container) {
        const message = document.createElement('div');
        message.className = 'thank-you-message';
        message.textContent = 'شكراً لتقييمك! 🙏';
        message.style.cssText = `
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: #4CAF50;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        `;
        
        container.style.position = 'relative';
        container.appendChild(message);
        
        setTimeout(() => {
            message.remove();
        }, 2000);
    }

    // إظهار إشعار
    showNotification(message, type = 'info') {
        if (window.enhancedNavManager && window.enhancedNavManager.showNotification) {
            window.enhancedNavManager.showNotification(message, type);
        } else {
            alert(message);
        }
    }

    // الحصول على معرف المستخدم
    getUserId() {
        let userId = localStorage.getItem('userId');
        if (!userId) {
            userId = 'user_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('userId', userId);
        }
        return userId;
    }

    // تحميل البيانات المحفوظة
    async loadRatings() {
        // تحميل التقييمات
        const savedRatings = localStorage.getItem('ratings');
        if (savedRatings) {
            this.ratings = new Map(JSON.parse(savedRatings));
        }

        // تحميل تقييمات المستخدم
        const savedUserRatings = localStorage.getItem('userRatings');
        if (savedUserRatings) {
            this.userRatings = new Map(JSON.parse(savedUserRatings));
        }

        // تحميل المراجعات
        const savedReviews = localStorage.getItem('reviews');
        if (savedReviews) {
            this.reviews = new Map(JSON.parse(savedReviews));
        }
    }
}

// تهيئة نظام التقييم
const ratingSystem = new RatingSystem();

// تصدير للاستخدام العام
window.ratingSystem = ratingSystem;
