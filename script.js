// تحسين وظيفة تمييز الرابط النشط باستخدام IntersectionObserver
const navAnchors = document.querySelectorAll('nav ul li a');
const sections = document.querySelectorAll('section');

const observerOptions = {
  root: null,
  rootMargin: '0px',
  threshold: 0.6,
};

const observerCallback = (entries) => {
  entries.forEach((entry) => {
    if (entry.isIntersecting) {
      const id = entry.target.id;
      navAnchors.forEach((link) => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${id}`) {
          link.classList.add('active');
        }
      });
    }
  });
};

const observer = new IntersectionObserver(observerCallback, observerOptions);
sections.forEach((section) => observer.observe(section));

// تحسين زر العودة للأعلى
const backBtn = document.getElementById('backToTop');
window.addEventListener('scroll', () => {
  if (window.scrollY > 300) {
    backBtn.style.display = 'block';
  } else {
    backBtn.style.display = 'none';
  }
});

backBtn.addEventListener('click', () => {
  window.scrollTo({ top: 0, behavior: 'smooth' });
});

// وظائف طي/فتح الأقسام مع تحسين الوصول
function toggleSection(header) {
  const section = header.closest('section');
  const content = section.querySelector('.content');
  const icon = header.querySelector('.toggle-icon');
  const sectionId = section.id;

  const isExpanded = content.style.display === 'block';
  content.style.display = isExpanded ? 'none' : 'block';
  section.classList.toggle('collapsed', isExpanded);
  icon.textContent = isExpanded ? '▶' : '▼';
  section.setAttribute('aria-expanded', !isExpanded);
  localStorage.setItem(sectionId, isExpanded ? 'collapsed' : 'expanded');
}

document.addEventListener('DOMContentLoaded', () => {
  document.querySelectorAll('section').forEach((sec) => {
    const state = localStorage.getItem(sec.id);
    const content = sec.querySelector('.content');
    const icon = sec.querySelector('.toggle-icon');
    const isExpanded = state === 'expanded';

    content.style.display = isExpanded ? 'block' : 'none';
    sec.classList.toggle('collapsed', !isExpanded);
    icon.textContent = isExpanded ? '▼' : '▶';
    sec.setAttribute('aria-expanded', isExpanded);
  });
});
