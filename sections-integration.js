// تكامل نظام الأقسام المحسّن مع النظام الحالي
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 بدء تكامل نظام الأقسام المحسّن...');
    
    // انتظار تحميل جميع المكونات
    setTimeout(() => {
        integrateEnhancedSections();
    }, 2000);
});

function integrateEnhancedSections() {
    console.log('🔗 تكامل الأنظمة...');
    
    // 1. تعطيل النظام القديم لطي/فتح الأقسام
    disableOldToggleSystem();
    
    // 2. تحديث الأقسام الموجودة لتتوافق مع النظام الجديد
    updateExistingSections();
    
    // 3. ربط النظام الجديد مع المكونات الأخرى
    connectWithOtherSystems();
    
    // 4. إضافة وظائف إضافية
    addEnhancedFeatures();
    
    console.log('✅ تم تكامل نظام الأقسام المحسّن بنجاح');
}

// تعطيل النظام القديم
function disableOldToggleSystem() {
    console.log('🔄 تعطيل النظام القديم...');
    
    // إزالة أحداث onclick القديمة
    document.querySelectorAll('section h2[onclick]').forEach(header => {
        header.removeAttribute('onclick');
        header.style.cursor = 'default';
    });
    
    // تعطيل وظيفة toggleSection القديمة
    if (window.toggleSection) {
        window.toggleSectionOld = window.toggleSection;
        window.toggleSection = function() {
            console.log('⚠️ النظام القديم معطل - استخدم النظام الجديد');
        };
    }
}

// تحديث الأقسام الموجودة
function updateExistingSections() {
    console.log('🔄 تحديث الأقسام الموجودة...');
    
    const sections = document.querySelectorAll('section[id]');
    sections.forEach((section, index) => {
        // إضافة خصائص البيانات المفقودة
        if (!section.getAttribute('data-category')) {
            section.setAttribute('data-category', getDefaultCategory(index));
        }
        
        if (!section.getAttribute('data-complexity')) {
            section.setAttribute('data-complexity', getDefaultComplexity(index));
        }
        
        if (!section.getAttribute('data-keywords')) {
            const title = section.querySelector('h2')?.textContent || '';
            section.setAttribute('data-keywords', generateKeywords(title));
        }
        
        // إضافة كلاس للتمييز
        section.classList.add('legacy-section');
    });
}

// ربط مع الأنظمة الأخرى
function connectWithOtherSystems() {
    console.log('🔗 ربط مع الأنظمة الأخرى...');
    
    // ربط مع نظام البحث الذكي
    if (window.smartSearch && window.enhancedSections) {
        const originalPerformSearch = window.smartSearch.performSearch;
        window.smartSearch.performSearch = async function(query) {
            const results = await originalPerformSearch.call(this, query);
            
            // تحديث تمييز الأقسام في جدول المحتويات
            if (results && results.length > 0) {
                results.forEach(result => {
                    window.enhancedSections.updateTocHighlight(result.id);
                });
            }
            
            return results;
        };
    }
    
    // ربط مع نظام التقييم
    if (window.ratingSystem && window.enhancedSections) {
        const originalSaveUserRating = window.ratingSystem.saveUserRating;
        window.ratingSystem.saveUserRating = function(sectionId, rating) {
            originalSaveUserRating.call(this, sectionId, rating);
            
            // تحديث تقدم القراءة
            window.enhancedSections.updateProgress();
        };
    }
    
    // ربط مع لوحة التحكم التحليلية
    if (window.analyticsDashboard && window.enhancedSections) {
        const originalTrackInteraction = window.analyticsDashboard.trackInteraction;
        window.analyticsDashboard.trackInteraction = function(type, data) {
            originalTrackInteraction.call(this, type, data);
            
            // تحديث إحصائيات الأقسام
            if (type === 'section_view' && data.sectionId) {
                window.enhancedSections.trackSectionView(data.sectionId);
            }
        };
    }
}

// إضافة وظائف محسّنة
function addEnhancedFeatures() {
    console.log('⚡ إضافة وظائف محسّنة...');
    
    // إضافة اختصارات لوحة المفاتيح للتنقل
    document.addEventListener('keydown', function(e) {
        // السهم الأيمن: القسم التالي
        if (e.key === 'ArrowRight' && e.ctrlKey) {
            e.preventDefault();
            if (window.enhancedSections) {
                window.enhancedSections.navigateToSection('next');
            }
        }
        
        // السهم الأيسر: القسم السابق
        if (e.key === 'ArrowLeft' && e.ctrlKey) {
            e.preventDefault();
            if (window.enhancedSections) {
                window.enhancedSections.navigateToSection('prev');
            }
        }
        
        // T: فتح/إغلاق جدول المحتويات
        if (e.key === 't' && e.ctrlKey) {
            e.preventDefault();
            toggleTableOfContents();
        }
        
        // M: فتح/إغلاق المتتبع العائم
        if (e.key === 'm' && e.ctrlKey) {
            e.preventDefault();
            toggleFloatingTracker();
        }
    });
    
    // إضافة زر العودة للأعلى محسّن
    addEnhancedBackToTop();
    
    // إضافة مؤشر التقدم في شريط المتصفح
    addProgressIndicator();
    
    // إضافة نظام الإشارات المرجعية المحسّن
    enhanceBookmarkSystem();
}

// تبديل جدول المحتويات
function toggleTableOfContents() {
    const toc = document.getElementById('enhanced-toc');
    if (toc) {
        const isVisible = toc.style.display !== 'none';
        toc.style.display = isVisible ? 'none' : 'block';
        
        if (window.enhancedNavManager) {
            window.enhancedNavManager.showNotification(
                isVisible ? 'تم إخفاء جدول المحتويات' : 'تم إظهار جدول المحتويات',
                'info'
            );
        }
    }
}

// تبديل المتتبع العائم
function toggleFloatingTracker() {
    const tracker = document.getElementById('floating-tracker');
    if (tracker) {
        const isVisible = tracker.style.display !== 'none';
        tracker.style.display = isVisible ? 'none' : 'block';
        
        if (window.enhancedNavManager) {
            window.enhancedNavManager.showNotification(
                isVisible ? 'تم إخفاء المتتبع' : 'تم إظهار المتتبع',
                'info'
            );
        }
    }
}

// إضافة زر العودة للأعلى محسّن
function addEnhancedBackToTop() {
    const backToTopHTML = `
        <button id="enhanced-back-to-top" class="enhanced-back-to-top" title="العودة للأعلى">
            <span class="back-to-top-icon">⬆️</span>
            <span class="back-to-top-progress"></span>
        </button>
    `;
    
    document.body.insertAdjacentHTML('beforeend', backToTopHTML);
    
    const backToTopBtn = document.getElementById('enhanced-back-to-top');
    const progressRing = backToTopBtn.querySelector('.back-to-top-progress');
    
    // إظهار/إخفاء الزر حسب التمرير
    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        
        if (scrollTop > 300) {
            backToTopBtn.style.display = 'flex';
            progressRing.style.background = `conic-gradient(var(--primary-color) ${scrollPercent * 3.6}deg, #e0e0e0 0deg)`;
        } else {
            backToTopBtn.style.display = 'none';
        }
    });
    
    // النقر للعودة للأعلى
    backToTopBtn.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
}

// إضافة مؤشر التقدم في شريط المتصفح
function addProgressIndicator() {
    const progressHTML = `
        <div id="page-progress-indicator" class="page-progress-indicator">
            <div class="progress-fill" id="pageProgressFill"></div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('afterbegin', progressHTML);
    
    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        
        const progressFill = document.getElementById('pageProgressFill');
        if (progressFill) {
            progressFill.style.width = `${Math.min(100, Math.max(0, scrollPercent))}%`;
        }
    });
}

// تحسين نظام الإشارات المرجعية
function enhanceBookmarkSystem() {
    // إضافة قائمة الإشارات المرجعية
    const bookmarksHTML = `
        <div id="bookmarks-panel" class="bookmarks-panel">
            <div class="bookmarks-header">
                <h3>📚 الإشارات المرجعية</h3>
                <button class="bookmarks-close" id="closeBookmarks">✕</button>
            </div>
            <div class="bookmarks-content" id="bookmarksContent">
                <p class="no-bookmarks">لا توجد إشارات مرجعية بعد</p>
            </div>
        </div>
        
        <button id="bookmarks-toggle" class="bookmarks-toggle" title="الإشارات المرجعية">
            📚
        </button>
    `;
    
    document.body.insertAdjacentHTML('beforeend', bookmarksHTML);
    
    // ربط الأحداث
    document.getElementById('bookmarks-toggle').addEventListener('click', toggleBookmarksPanel);
    document.getElementById('closeBookmarks').addEventListener('click', closeBookmarksPanel);
    
    // تحديث قائمة الإشارات المرجعية
    updateBookmarksList();
}

// تبديل لوحة الإشارات المرجعية
function toggleBookmarksPanel() {
    const panel = document.getElementById('bookmarks-panel');
    const isOpen = panel.classList.contains('open');
    
    if (isOpen) {
        panel.classList.remove('open');
    } else {
        panel.classList.add('open');
        updateBookmarksList();
    }
}

// إغلاق لوحة الإشارات المرجعية
function closeBookmarksPanel() {
    document.getElementById('bookmarks-panel').classList.remove('open');
}

// تحديث قائمة الإشارات المرجعية
function updateBookmarksList() {
    const bookmarks = JSON.parse(localStorage.getItem('bookmarkedSections') || '[]');
    const container = document.getElementById('bookmarksContent');
    
    if (bookmarks.length === 0) {
        container.innerHTML = '<p class="no-bookmarks">لا توجد إشارات مرجعية بعد</p>';
        return;
    }
    
    const bookmarksHTML = bookmarks.map(sectionId => {
        const section = document.getElementById(sectionId);
        const title = section?.querySelector('h2')?.textContent || sectionId;
        
        return `
            <div class="bookmark-item" data-section="${sectionId}">
                <span class="bookmark-title">${title}</span>
                <button class="bookmark-go" onclick="goToBookmark('${sectionId}')">انتقال</button>
                <button class="bookmark-remove" onclick="removeBookmark('${sectionId}')">حذف</button>
            </div>
        `;
    }).join('');
    
    container.innerHTML = bookmarksHTML;
}

// الانتقال إلى إشارة مرجعية
function goToBookmark(sectionId) {
    if (window.enhancedSections) {
        window.enhancedSections.scrollToSection(sectionId);
    } else {
        document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
    }
    closeBookmarksPanel();
}

// حذف إشارة مرجعية
function removeBookmark(sectionId) {
    const bookmarks = JSON.parse(localStorage.getItem('bookmarkedSections') || '[]');
    const updatedBookmarks = bookmarks.filter(id => id !== sectionId);
    localStorage.setItem('bookmarkedSections', JSON.stringify(updatedBookmarks));
    
    // تحديث زر الإشارة المرجعية في القسم
    if (window.enhancedSections) {
        window.enhancedSections.updateBookmarkButton(sectionId, false);
    }
    
    updateBookmarksList();
}

// وظائف مساعدة لتحديد الفئات والتعقيد
function getDefaultCategory(index) {
    const categories = ['basics', 'basics', 'analysis', 'comparison', 'tools', 'tools', 'advanced', 'advanced', 'summary'];
    return categories[index] || 'basics';
}

function getDefaultComplexity(index) {
    const complexities = ['beginner', 'intermediate', 'advanced', 'intermediate', 'advanced', 'intermediate', 'advanced', 'advanced', 'intermediate'];
    return complexities[index] || 'intermediate';
}

function generateKeywords(title) {
    // استخراج كلمات مفتاحية بسيطة من العنوان
    return title.replace(/[^\u0600-\u06FF\s]/g, '').split(/\s+/).filter(word => word.length > 2).join(' ');
}

// تصدير الوظائف للاستخدام العام
window.sectionsIntegration = {
    toggleTableOfContents,
    toggleFloatingTracker,
    goToBookmark,
    removeBookmark,
    updateBookmarksList
};

console.log('🔗 تم تحميل نظام تكامل الأقسام المحسّن');
