// نظام البحث الذكي المتقدم
class SmartSearchEngine {
    constructor() {
        this.searchIndex = new Map();
        this.semanticCache = new Map();
        this.searchHistory = [];
        this.suggestions = [];
        this.isInitialized = false;
        
        this.init();
    }

    async init() {
        await this.buildSearchIndex();
        await this.loadSearchHistory();
        this.setupAutoComplete();
        this.isInitialized = true;
        console.log('🔍 نظام البحث الذكي جاهز');
    }

    // بناء فهرس البحث المتقدم
    async buildSearchIndex() {
        const sections = document.querySelectorAll('section[data-category]');
        
        sections.forEach((section, index) => {
            const content = {
                id: section.id,
                title: section.querySelector('h2')?.textContent || '',
                content: section.textContent.toLowerCase(),
                category: section.getAttribute('data-category'),
                complexity: section.getAttribute('data-complexity'),
                keywords: section.getAttribute('data-keywords')?.split(' ') || [],
                element: section,
                searchScore: 0
            };

            // إنشاء مفاتيح بحث متعددة
            const searchKeys = [
                ...content.keywords,
                ...content.title.split(' '),
                ...this.extractKeyPhrases(content.content)
            ];

            searchKeys.forEach(key => {
                if (key.length > 2) {
                    if (!this.searchIndex.has(key)) {
                        this.searchIndex.set(key, []);
                    }
                    this.searchIndex.get(key).push(content);
                }
            });
        });
    }

    // استخراج العبارات المفتاحية
    extractKeyPhrases(text) {
        const words = text.split(/\s+/);
        const phrases = [];
        
        // عبارات من كلمتين
        for (let i = 0; i < words.length - 1; i++) {
            phrases.push(words[i] + ' ' + words[i + 1]);
        }
        
        // عبارات من ثلاث كلمات
        for (let i = 0; i < words.length - 2; i++) {
            phrases.push(words[i] + ' ' + words[i + 1] + ' ' + words[i + 2]);
        }
        
        return phrases.filter(phrase => phrase.length > 5);
    }

    // البحث الدلالي المتقدم
    async semanticSearch(query) {
        if (!this.isInitialized) await this.init();
        
        const results = new Map();
        const queryTerms = query.toLowerCase().split(/\s+/);
        
        // البحث المباشر
        queryTerms.forEach(term => {
            if (this.searchIndex.has(term)) {
                this.searchIndex.get(term).forEach(item => {
                    if (!results.has(item.id)) {
                        results.set(item.id, { ...item, score: 0 });
                    }
                    results.get(item.id).score += 10; // نقاط للتطابق المباشر
                });
            }
        });

        // البحث الضبابي (Fuzzy Search)
        this.searchIndex.forEach((items, key) => {
            queryTerms.forEach(term => {
                const similarity = this.calculateSimilarity(term, key);
                if (similarity > 0.7) {
                    items.forEach(item => {
                        if (!results.has(item.id)) {
                            results.set(item.id, { ...item, score: 0 });
                        }
                        results.get(item.id).score += similarity * 5;
                    });
                }
            });
        });

        // البحث في المحتوى
        queryTerms.forEach(term => {
            document.querySelectorAll('section[data-category]').forEach(section => {
                const content = section.textContent.toLowerCase();
                const matches = (content.match(new RegExp(term, 'g')) || []).length;
                if (matches > 0) {
                    const id = section.id;
                    if (!results.has(id)) {
                        results.set(id, {
                            id,
                            title: section.querySelector('h2')?.textContent || '',
                            element: section,
                            score: 0
                        });
                    }
                    results.get(id).score += matches * 2;
                }
            });
        });

        // ترتيب النتائج حسب النقاط
        const sortedResults = Array.from(results.values())
            .sort((a, b) => b.score - a.score)
            .slice(0, 10); // أفضل 10 نتائج

        this.saveSearchQuery(query, sortedResults.length);
        return sortedResults;
    }

    // حساب التشابه بين النصوص
    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        
        if (longer.length === 0) return 1.0;
        
        const distance = this.levenshteinDistance(longer, shorter);
        return (longer.length - distance) / longer.length;
    }

    // مسافة ليفنشتاين للبحث الضبابي
    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }

    // الإكمال التلقائي
    setupAutoComplete() {
        const searchBox = document.getElementById('searchBox');
        if (!searchBox) return;

        const suggestionsContainer = this.createSuggestionsContainer();
        
        searchBox.addEventListener('input', this.debounce(async (e) => {
            const query = e.target.value.trim();
            if (query.length < 2) {
                suggestionsContainer.style.display = 'none';
                return;
            }

            const suggestions = await this.generateSuggestions(query);
            this.displaySuggestions(suggestions, suggestionsContainer);
        }, 200));

        // إخفاء الاقتراحات عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!searchBox.contains(e.target) && !suggestionsContainer.contains(e.target)) {
                suggestionsContainer.style.display = 'none';
            }
        });
    }

    // إنشاء حاوي الاقتراحات
    createSuggestionsContainer() {
        const container = document.createElement('div');
        container.className = 'search-suggestions';
        container.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        `;

        const searchContainer = document.querySelector('.search-filter-container');
        if (searchContainer) {
            searchContainer.style.position = 'relative';
            searchContainer.appendChild(container);
        }

        return container;
    }

    // توليد الاقتراحات
    async generateSuggestions(query) {
        const suggestions = [];
        const queryLower = query.toLowerCase();

        // اقتراحات من الكلمات المفتاحية
        this.searchIndex.forEach((items, key) => {
            if (key.includes(queryLower) && suggestions.length < 8) {
                suggestions.push({
                    text: key,
                    type: 'keyword',
                    count: items.length
                });
            }
        });

        // اقتراحات من التاريخ
        const historyMatches = this.searchHistory
            .filter(item => item.query.toLowerCase().includes(queryLower))
            .slice(0, 3);
        
        historyMatches.forEach(item => {
            suggestions.push({
                text: item.query,
                type: 'history',
                count: item.results
            });
        });

        return suggestions.slice(0, 8);
    }

    // عرض الاقتراحات
    displaySuggestions(suggestions, container) {
        if (suggestions.length === 0) {
            container.style.display = 'none';
            return;
        }

        container.innerHTML = suggestions.map(suggestion => `
            <div class="suggestion-item" data-query="${suggestion.text}">
                <span class="suggestion-icon">
                    ${suggestion.type === 'history' ? '🕒' : '🔍'}
                </span>
                <span class="suggestion-text">${suggestion.text}</span>
                <span class="suggestion-count">${suggestion.count} نتيجة</span>
            </div>
        `).join('');

        // ربط أحداث النقر
        container.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                const query = item.getAttribute('data-query');
                document.getElementById('searchBox').value = query;
                this.performSearch(query);
                container.style.display = 'none';
            });
        });

        container.style.display = 'block';
    }

    // تنفيذ البحث
    async performSearch(query) {
        const loadingIndicator = document.getElementById('loadingIndicator');
        if (loadingIndicator) loadingIndicator.style.display = 'block';

        try {
            const results = await this.semanticSearch(query);
            this.displaySearchResults(results, query);
            this.highlightSearchTerms(query);
        } catch (error) {
            console.error('خطأ في البحث:', error);
        } finally {
            if (loadingIndicator) loadingIndicator.style.display = 'none';
        }
    }

    // عرض نتائج البحث
    displaySearchResults(results, query) {
        const sections = document.querySelectorAll('section[data-category]');
        
        sections.forEach(section => {
            const result = results.find(r => r.id === section.id);
            if (result) {
                section.style.display = 'block';
                section.style.order = -result.score; // ترتيب حسب النقاط
                section.classList.add('search-result');
            } else {
                section.style.display = 'none';
                section.classList.remove('search-result');
            }
        });

        this.updateSearchStats(results.length, query);
    }

    // تمييز مصطلحات البحث
    highlightSearchTerms(query) {
        const terms = query.toLowerCase().split(/\s+/);
        const sections = document.querySelectorAll('section.search-result');
        
        sections.forEach(section => {
            const walker = document.createTreeWalker(
                section,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }

            textNodes.forEach(textNode => {
                let text = textNode.textContent;
                terms.forEach(term => {
                    if (term.length > 2) {
                        const regex = new RegExp(`(${term})`, 'gi');
                        text = text.replace(regex, '<mark>$1</mark>');
                    }
                });
                
                if (text !== textNode.textContent) {
                    const span = document.createElement('span');
                    span.innerHTML = text;
                    textNode.parentNode.replaceChild(span, textNode);
                }
            });
        });
    }

    // تحديث إحصائيات البحث
    updateSearchStats(resultCount, query) {
        const totalSections = document.querySelectorAll('section[data-category]').length;
        console.log(`🔍 البحث عن "${query}": ${resultCount} من أصل ${totalSections} نتيجة`);
        
        // تحديث عنوان الصفحة
        document.title = `بحث: ${query} - ${resultCount} نتيجة`;
    }

    // حفظ استعلام البحث
    saveSearchQuery(query, resultCount) {
        const searchItem = {
            query,
            results: resultCount,
            timestamp: new Date().toISOString()
        };

        this.searchHistory.unshift(searchItem);
        this.searchHistory = this.searchHistory.slice(0, 50); // الاحتفاظ بآخر 50 بحث
        
        localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
    }

    // تحميل تاريخ البحث
    async loadSearchHistory() {
        const saved = localStorage.getItem('searchHistory');
        if (saved) {
            this.searchHistory = JSON.parse(saved);
        }
    }

    // وظيفة التأخير
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // إعادة تعيين البحث
    resetSearch() {
        const sections = document.querySelectorAll('section[data-category]');
        sections.forEach(section => {
            section.style.display = 'block';
            section.style.order = '';
            section.classList.remove('search-result');
        });

        // إزالة التمييز
        document.querySelectorAll('mark').forEach(mark => {
            mark.outerHTML = mark.innerHTML;
        });

        document.getElementById('searchBox').value = '';
        document.title = 'بحث هندسة البرمبت وبناء الوكلاء الذكيين';
    }
}

// تهيئة نظام البحث الذكي
const smartSearch = new SmartSearchEngine();

// تصدير للاستخدام العام
window.smartSearch = smartSearch;
