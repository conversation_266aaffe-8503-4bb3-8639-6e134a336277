/* ملف CSS منفصل لتحسين الأداء */

/* متغيرات CSS للألوان والقيم */
:root {
    --primary-color: #4535cd;
    --secondary-color: #81cdf8;
    --accent-color: #3a2ba8;
    --background-color: #f5f5f5;
    --text-color: #333333;
    --white: #ffffff;
    --border-color: #dddddd;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* إعدادات عامة محسّنة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    scroll-behavior: smooth;
}

/* تحسينات الهيدر */
header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 20px;
    text-align: center;
    color: var(--white);
    position: relative;
    overflow: hidden;
}

header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

header h1 {
    margin: 0;
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    position: relative;
    z-index: 1;
}

/* الشريط العلوي المحسّن */
.top-bar {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: var(--white);
    padding: 8px 0;
    font-size: 14px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.top-bar-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: flex-end;
}

.top-bar-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* محدد اللغة */
.language-selector select {
    background: rgba(255,255,255,0.1);
    color: var(--white);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 15px;
    padding: 5px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.language-selector select:hover {
    background: rgba(255,255,255,0.2);
}

/* تبديل الوضع المظلم */
.theme-toggle button {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle button:hover {
    background: rgba(255,255,255,0.2);
    transform: scale(1.1);
}

/* قائمة المستخدم */
.user-menu {
    position: relative;
}

.user-btn {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 20px;
    padding: 6px 12px;
    color: var(--white);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    transition: var(--transition);
}

.user-btn:hover {
    background: rgba(255,255,255,0.2);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    min-width: 180px;
    display: none;
    z-index: 1001;
}

.user-dropdown.show {
    display: block;
    animation: fadeInDown 0.3s ease;
}

.dropdown-item {
    display: block;
    padding: 10px 15px;
    color: var(--text-color);
    text-decoration: none;
    font-size: 13px;
    transition: var(--transition);
}

.dropdown-item:hover {
    background: #f8f9fa;
    color: var(--primary-color);
}

.dropdown-divider {
    height: 1px;
    background: var(--border-color);
    margin: 5px 0;
}

/* شريط التنقل الرئيسي المحسّن */
.main-nav {
    background: linear-gradient(135deg, var(--white), #f8f9fa);
    border-bottom: 2px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 999;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 70px;
}

/* العلامة التجارية المحسّنة */
.nav-brand .logo {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: var(--text-color);
}

.logo-icon {
    font-size: 32px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.logo-subtitle {
    font-size: 11px;
    color: var(--accent-color);
    font-weight: 400;
    opacity: 0.8;
}

/* قائمة التنقل المحسّنة */
.nav-menu {
    display: flex;
    gap: 30px;
    align-items: center;
}

.nav-category {
    position: relative;
}

.category-title {
    font-size: 11px;
    color: var(--accent-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 8px;
    display: block;
}

.nav-items {
    display: flex;
    gap: 15px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 20px;
    text-decoration: none;
    color: var(--text-color);
    font-size: 13px;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    border: 1px solid transparent;
}

.nav-item:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(69, 53, 205, 0.3);
    border-color: var(--primary-color);
}

.nav-icon {
    font-size: 14px;
}

.nav-text {
    font-weight: 500;
}

/* Tooltips للتنقل */
.nav-item[data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: var(--white);
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1002;
}

/* أزرار الإجراءات */
.nav-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.action-btn {
    background: rgba(69, 53, 205, 0.1);
    border: 1px solid rgba(69, 53, 205, 0.2);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    color: var(--primary-color);
}

.action-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(69, 53, 205, 0.3);
}

.action-icon {
    font-size: 14px;
}

nav .nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: auto;
    padding: 0 20px;
}

nav .logo {
    font-weight: bold;
    color: var(--primary-color);
    font-size: 1.2rem;
}

nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 10px;
}

nav ul li a {
    text-decoration: none;
    color: var(--primary-color);
    font-weight: 500;
    padding: 12px 16px;
    border-radius: 25px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

nav ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

nav ul li a:hover::before {
    left: 100%;
}

nav ul li a:hover,
nav ul li a.active {
    background-color: var(--secondary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(69, 53, 205, 0.3);
}

/* تحسينات الحاوي الرئيسي */
.container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 0 20px;
}

/* تحسينات الأقسام */
section {
    background-color: var(--white);
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid transparent;
}

section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: var(--secondary-color);
}

section h2 {
    margin: 0;
    padding: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

section h2::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s;
}

section h2:hover::before {
    left: 100%;
}

section h2:hover {
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
}

section .content {
    display: none;
    padding: 25px;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

section.expanded .content {
    display: block;
}

/* تحسينات الجداول */
.table-container {
    overflow-x: auto;
    margin: 20px 0;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--white);
    font-size: 14px;
}

table th {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    padding: 15px 12px;
    text-align: right;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 2;
    border-bottom: 2px solid var(--accent-color);
}

table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

table tbody tr:nth-child(odd) {
    background-color: #fafafa;
}

table tbody tr:hover {
    background-color: #f0f8ff;
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* تحسينات شريط البحث */
.search-filter-container {
    background: linear-gradient(135deg, var(--white), #f8f9fa);
    padding: 25px;
    margin-bottom: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    align-items: center;
    border: 1px solid var(--border-color);
}

.search-box,
.filter-select {
    padding: 12px 18px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 14px;
    transition: var(--transition);
    background-color: var(--white);
}

.search-box {
    flex: 1;
    min-width: 250px;
}

.search-box:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(69, 53, 205, 0.1);
    transform: translateY(-1px);
}

/* زر القائمة للموبايل */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: var(--transition);
}

.mobile-menu-toggle span {
    width: 22px;
    height: 2px;
    background-color: var(--primary-color);
    margin: 3px 0;
    transition: var(--transition);
    border-radius: 1px;
}

.mobile-menu-toggle.open span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.mobile-menu-toggle.open span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.open span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* تحسينات الاستجابة للأجهزة المحمولة */
@media (max-width: 1024px) {
    .nav-menu {
        gap: 20px;
    }

    .nav-category {
        margin-bottom: 0;
    }

    .category-title {
        font-size: 10px;
    }

    .nav-item {
        padding: 6px 10px;
        font-size: 12px;
    }

    .nav-actions {
        gap: 6px;
    }

    .action-btn {
        width: 32px;
        height: 32px;
    }
}

@media (max-width: 768px) {
    /* الشريط العلوي للموبايل */
    .top-bar {
        padding: 6px 0;
    }

    .top-bar-right {
        gap: 15px;
    }

    .language-selector select {
        padding: 4px 8px;
        font-size: 11px;
    }

    .theme-toggle button {
        width: 28px;
        height: 28px;
    }

    .user-btn {
        padding: 4px 8px;
        font-size: 11px;
    }

    /* التنقل الرئيسي للموبايل */
    .nav-container {
        min-height: 60px;
        padding: 0 15px;
    }

    .logo-icon {
        font-size: 24px;
    }

    .logo-title {
        font-size: 16px;
    }

    .logo-subtitle {
        font-size: 10px;
    }

    .nav-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--white);
        border-top: 1px solid var(--border-color);
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        flex-direction: column;
        gap: 0;
        padding: 20px;
        display: none;
        z-index: 998;
    }

    .nav-menu.show {
        display: flex;
        animation: slideDown 0.3s ease;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .nav-category {
        width: 100%;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid var(--border-color);
    }

    .nav-category:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .category-title {
        font-size: 12px;
        margin-bottom: 10px;
        color: var(--primary-color);
    }

    .nav-items {
        flex-direction: column;
        gap: 8px;
    }

    .nav-item {
        width: 100%;
        padding: 12px 15px;
        border-radius: 8px;
        justify-content: flex-start;
        background: #f8f9fa;
        border: 1px solid var(--border-color);
    }

    .nav-actions {
        position: absolute;
        top: 15px;
        left: 15px;
        gap: 8px;
    }

    .action-btn {
        width: 30px;
        height: 30px;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    /* تحسينات البحث للموبايل */
    .search-filter-container {
        flex-direction: column;
        align-items: stretch;
        padding: 15px;
        gap: 12px;
    }

    .search-box,
    .filter-select {
        min-width: 100%;
        margin-bottom: 0;
    }

    /* تحسينات الجداول للموبايل */
    table {
        font-size: 11px;
        min-width: 600px;
    }

    table th,
    table td {
        padding: 6px 4px;
        word-wrap: break-word;
    }

    /* تحسينات الأقسام للموبايل */
    section h2 {
        padding: 12px 15px;
        font-size: 0.95rem;
    }

    section .content {
        padding: 15px;
    }

    /* تحسينات الحاوي للموبايل */
    .container {
        padding: 0 15px;
    }
}

@media (max-width: 480px) {
    .top-bar-right {
        gap: 10px;
    }

    .user-name {
        display: none;
    }

    .nav-container {
        padding: 0 10px;
    }

    .logo-subtitle {
        display: none;
    }

    .nav-actions {
        gap: 6px;
    }

    .action-btn {
        width: 28px;
        height: 28px;
    }

    .action-icon {
        font-size: 12px;
    }

    .search-filter-container {
        padding: 12px;
    }

    .container {
        padding: 0 10px;
    }
}

/* تحسينات إضافية للأداء */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تحسينات الطباعة */
@media print {
    nav,
    #backToTop,
    .search-filter-container,
    .loading-indicator {
        display: none !important;
    }

    section .content {
        display: block !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: black;
        background: white;
    }

    section {
        break-inside: avoid;
        margin-bottom: 20pt;
    }

    table {
        break-inside: avoid;
    }
}

/* تحسينات إضافية للأداء والتفاعل */
.toggle-icon {
    transition: transform 0.3s ease;
    display: inline-block;
}

.expanded .toggle-icon {
    transform: rotate(90deg);
}

/* تأثيرات التحميل */
.loading-indicator {
    display: none;
    text-align: center;
    padding: 20px;
    color: var(--primary-color);
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات زر العودة للأعلى */
#backToTop {
    display: none;
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    font-size: 20px;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(69, 53, 205, 0.3);
    z-index: 1000;
}

#backToTop:hover {
    background: var(--accent-color);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(69, 53, 205, 0.4);
}

/* تحسينات إضافية للجداول */
table tbody tr {
    transition: all 0.2s ease;
}

table tbody tr:hover {
    background-color: #e3f2fd !important;
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* تحسينات النصوص المميزة */
mark {
    background-color: #ffeb3b;
    color: #333;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

/* تحسينات الروابط */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--accent-color);
    text-decoration: underline;
}

/* تحسينات الكود */
pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 15px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
}

code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

/* تحسينات القوائم */
ul, ol {
    padding-right: 20px;
}

li {
    margin-bottom: 8px;
    line-height: 1.6;
}

/* تحسينات العناوين الفرعية */
h3 {
    color: var(--primary-color);
    margin-top: 25px;
    margin-bottom: 15px;
    font-size: 1.3em;
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 5px;
}

/* تحسينات الفقرات */
p {
    margin-bottom: 15px;
    text-align: justify;
}

/* تحسينات النصوص المهمة */
strong {
    color: var(--primary-color);
    font-weight: 600;
}

em {
    color: var(--accent-color);
    font-style: italic;
}

/* تحسينات الحدود والظلال */
.card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 20px;
    margin-bottom: 20px;
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

/* تحسينات الأزرار */
.btn {
    display: inline-block;
    padding: 12px 24px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    text-decoration: none;
}

.btn:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(69, 53, 205, 0.3);
}

.btn-secondary {
    background: var(--secondary-color);
}

.btn-secondary:hover {
    background: #6bb6ff;
}

/* تحسينات الرسائل والتنبيهات */
.alert {
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border-left: 4px solid;
}

.alert-info {
    background-color: #d1ecf1;
    border-left-color: #17a2b8;
    color: #0c5460;
}

.alert-success {
    background-color: #d4edda;
    border-left-color: #28a745;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    border-left-color: #ffc107;
    color: #856404;
}

.alert-error {
    background-color: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
}

/* الوضع المظلم */
body.dark-mode {
    --background-color: #1a1a1a;
    --text-color: #e0e0e0;
    --white: #2d2d2d;
    --border-color: #404040;
    --shadow: 0 2px 4px rgba(0,0,0,0.3);
}

body.dark-mode .top-bar {
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
}

body.dark-mode .main-nav {
    background: linear-gradient(135deg, #2d2d2d, #3a3a3a);
}

body.dark-mode section h2 {
    background: linear-gradient(135deg, #3a3a3a, #4a4a4a);
}

body.dark-mode .search-filter-container {
    background: linear-gradient(135deg, #2d2d2d, #3a3a3a);
}

body.dark-mode table th {
    background: linear-gradient(135deg, #3a3a3a, #4a4a4a);
}

body.dark-mode table tbody tr:nth-child(odd) {
    background-color: #333333;
}

body.dark-mode table tbody tr:hover {
    background-color: #404040 !important;
}

body.dark-mode .nav-item:hover {
    background: linear-gradient(135deg, #4a4a4a, #5a5a5a);
}

body.dark-mode .action-btn {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: #e0e0e0;
}

body.dark-mode .action-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* الإشعارات */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--white);
    color: var(--text-color);
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border-left: 4px solid var(--primary-color);
    z-index: 10000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 300px;
    font-size: 14px;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-left-color: #28a745;
    background: #d4edda;
    color: #155724;
}

.notification.error {
    border-left-color: #dc3545;
    background: #f8d7da;
    color: #721c24;
}

.notification.warning {
    border-left-color: #ffc107;
    background: #fff3cd;
    color: #856404;
}

.notification.info {
    border-left-color: #17a2b8;
    background: #d1ecf1;
    color: #0c5460;
}

/* تحسينات المفضلة */
.action-btn.bookmarked {
    background: #ffc107;
    color: #212529;
}

.action-btn.bookmarked:hover {
    background: #e0a800;
}

/* تحسينات إضافية للتفاعل */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات الأداء للرسوم المتحركة */
.nav-item,
.action-btn,
.user-btn,
.theme-toggle button {
    will-change: transform;
}

/* تحسينات إضافية للوضع المظلم */
body.dark-mode .user-dropdown {
    background: #2d2d2d;
    border-color: #404040;
}

body.dark-mode .dropdown-item {
    color: #e0e0e0;
}

body.dark-mode .dropdown-item:hover {
    background: #404040;
    color: var(--primary-color);
}

body.dark-mode .search-box,
body.dark-mode .filter-select {
    background: #3a3a3a;
    border-color: #555555;
    color: #e0e0e0;
}

body.dark-mode .search-box:focus,
body.dark-mode .filter-select:focus {
    border-color: var(--primary-color);
    background: #404040;
}

body.dark-mode mark {
    background-color: #ffc107;
    color: #212529;
}

body.dark-mode pre {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #e0e0e0;
}

body.dark-mode code {
    background-color: #2d2d2d;
    color: #e0e0e0;
}

/* تحسينات الانتقال للوضع المظلم */
body,
.top-bar,
.main-nav,
section,
.search-filter-container,
table,
.user-dropdown,
.search-box,
.filter-select,
pre,
code {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* أنماط البحث الذكي */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.suggestion-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid #f0f0f0;
}

.suggestion-item:hover {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-icon {
    margin-left: 10px;
    font-size: 14px;
}

.suggestion-text {
    flex: 1;
    font-size: 14px;
    color: var(--text-color);
}

.suggestion-count {
    font-size: 12px;
    color: #666;
    background: #f0f0f0;
    padding: 2px 8px;
    border-radius: 10px;
}

/* أنماط المساعد الذكي */
.ai-assistant {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 350px;
    height: 500px;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
    z-index: 10000;
    display: flex;
    flex-direction: column;
    transform: translateY(100vh);
    transition: transform 0.3s ease;
    border: 1px solid var(--border-color);
}

.ai-assistant.open {
    transform: translateY(0);
}

.assistant-header {
    display: flex;
    align-items: center;
    padding: 15px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    border-radius: 15px 15px 0 0;
}

.assistant-avatar {
    font-size: 24px;
    margin-left: 10px;
}

.assistant-info h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.assistant-status {
    font-size: 12px;
    opacity: 0.8;
}

.assistant-close {
    margin-right: auto;
    background: none;
    border: none;
    color: var(--white);
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
}

.assistant-close:hover {
    background: rgba(255,255,255,0.2);
}

.assistant-chat {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    background: #f8f9fa;
}

.message {
    display: flex;
    margin-bottom: 15px;
    animation: fadeInUp 0.3s ease;
}

.message-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    margin-left: 10px;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: var(--primary-color);
    color: var(--white);
}

.assistant-message .message-avatar {
    background: #e0e0e0;
}

.message-content {
    flex: 1;
    background: var(--white);
    padding: 10px 15px;
    border-radius: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: relative;
}

.user-message .message-content {
    background: var(--primary-color);
    color: var(--white);
}

.message-content p {
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.message-content ul {
    margin: 8px 0;
    padding-right: 20px;
}

.message-time {
    font-size: 10px;
    color: #999;
    margin-top: 5px;
    text-align: left;
}

.assistant-input {
    padding: 15px;
    border-top: 1px solid var(--border-color);
    background: var(--white);
    border-radius: 0 0 15px 15px;
}

.quick-questions {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.quick-btn {
    background: #f0f0f0;
    border: none;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 11px;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-color);
}

.quick-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

.input-container {
    display: flex;
    gap: 10px;
    align-items: center;
}

.input-container input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    font-size: 14px;
    outline: none;
    transition: var(--transition);
}

.input-container input:focus {
    border-color: var(--primary-color);
}

.input-container button {
    background: var(--primary-color);
    border: none;
    color: var(--white);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-container button:hover {
    background: var(--accent-color);
}



.notification-dot {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 12px;
    height: 12px;
    background: #ff4444;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.typing-indicator .message-content {
    padding: 15px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: #ccc;
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-10px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* أنماط نظام التقييم */
.rating-widget {
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    margin-top: 25px;
    transition: var(--transition);
}

.rating-widget:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.rating-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.rating-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 16px;
}

.rating-stats {
    display: flex;
    align-items: center;
    gap: 10px;
}

.average-rating {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

.stars-display,
.stars-input {
    display: flex;
    gap: 2px;
}

.star-display,
.star-input {
    font-size: 16px;
    color: #ddd;
    transition: var(--transition);
}

.star-input {
    cursor: pointer;
}

.star-display.filled,
.star-input.filled {
    color: #ffc107;
}

.star-input:hover {
    transform: scale(1.2);
}

.rating-count {
    font-size: 12px;
    color: #666;
}

.rating-actions {
    margin-bottom: 20px;
}

.user-rating {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding: 10px;
    background: var(--white);
    border-radius: 8px;
}

.rating-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.rating-btn {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    background: var(--white);
    cursor: pointer;
    font-size: 13px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 5px;
}

.rating-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.rating-btn.helpful:hover {
    background: #28a745;
    border-color: #28a745;
}

.rating-btn.not-helpful:hover {
    background: #dc3545;
    border-color: #dc3545;
}

.rating-breakdown {
    margin-bottom: 20px;
}

.breakdown-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
    font-size: 14px;
}

.breakdown-item span:first-child {
    min-width: 60px;
    font-weight: 500;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
}

.breakdown-item span:last-child {
    min-width: 30px;
    text-align: center;
    font-weight: bold;
    color: var(--primary-color);
}

.recent-reviews {
    border-top: 1px solid var(--border-color);
    padding-top: 15px;
}

.review-item {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 12px;
    color: #666;
}

.reviewer {
    font-weight: 500;
}

.review-content p {
    margin: 0 0 10px 0;
    line-height: 1.5;
}

.review-tags {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.review-tags .tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
}

.no-reviews {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 20px;
}

/* نافذة المراجعة */
.review-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10001;
}

.modal-content {
    background: var(--white);
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 40px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
}

.modal-close:hover {
    background: #f0f0f0;
}

.modal-body {
    padding: 20px;
}

.rating-categories {
    margin-bottom: 20px;
}

.category-rating {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.category-rating label {
    font-weight: 500;
    color: var(--text-color);
}

.review-text {
    margin-bottom: 20px;
}

.review-text label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.review-text textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-family: inherit;
    font-size: 14px;
    resize: vertical;
    min-height: 100px;
}

.review-text textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.review-tags label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: var(--text-color);
}

.tags-container {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tag-btn {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 15px;
    background: var(--white);
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition);
}

.tag-btn:hover,
.tag-btn.selected {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid var(--border-color);
}

.submit-btn,
.cancel-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
}

.submit-btn {
    background: var(--primary-color);
    color: var(--white);
}

.submit-btn:hover {
    background: var(--accent-color);
}

.cancel-btn {
    background: #f0f0f0;
    color: var(--text-color);
}

.cancel-btn:hover {
    background: #e0e0e0;
}

.thank-you-message {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: #4CAF50;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    animation: fadeInUp 0.3s ease;
}

/* أنماط لوحة التحكم التحليلية */
.analytics-dashboard {
    position: fixed;
    top: 0;
    right: -100%;
    width: 400px;
    height: 100vh;
    background: var(--white);
    box-shadow: -5px 0 20px rgba(0,0,0,0.1);
    z-index: 10002;
    transition: right 0.3s ease;
    overflow-y: auto;
    border-left: 1px solid var(--border-color);
}

.analytics-dashboard.open {
    right: 0;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    position: sticky;
    top: 0;
    z-index: 1;
}

.dashboard-header h2 {
    margin: 0;
    font-size: 18px;
}

.dashboard-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.dashboard-controls select {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: var(--white);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
}

.export-btn,
.close-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: var(--white);
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition);
}

.export-btn:hover,
.close-btn:hover {
    background: rgba(255,255,255,0.3);
}

.dashboard-content {
    padding: 20px;
}

.quick-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 25px;
}

.metric-card {
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: var(--transition);
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.metric-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
}

.metric-info {
    flex: 1;
}

.metric-value {
    display: block;
    font-size: 20px;
    font-weight: bold;
    color: var(--primary-color);
    line-height: 1;
}

.metric-label {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.charts-container {
    margin-bottom: 25px;
}

.chart-card {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
}

.chart-card h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: var(--primary-color);
}

.bar-chart {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.bar-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.bar-label {
    font-size: 12px;
    color: var(--text-color);
    font-weight: 500;
}

.bar-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.bar-fill {
    height: 20px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
    transition: width 0.3s ease;
    min-width: 20px;
}

.bar-value {
    font-size: 12px;
    font-weight: bold;
    color: var(--primary-color);
    min-width: 30px;
}

.data-tables {
    margin-bottom: 25px;
}

.table-card {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
}

.table-card h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: var(--primary-color);
}

.data-table {
    max-height: 200px;
    overflow-y: auto;
}

.data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
}

.data-row:last-child {
    border-bottom: none;
}

.search-query,
.interaction-type {
    font-weight: 500;
    color: var(--text-color);
    flex: 1;
}

.search-results,
.interaction-target {
    color: #666;
    font-size: 11px;
}

.search-time,
.interaction-time {
    color: #999;
    font-size: 10px;
    min-width: 60px;
    text-align: left;
}

.no-data {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 20px;
    margin: 0;
}

.performance-stats {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 20px;
}

.performance-stats h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: var(--primary-color);
}

.performance-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.performance-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.performance-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.performance-value {
    font-size: 18px;
    font-weight: bold;
    color: var(--primary-color);
}



.live-indicator {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 8px;
    height: 8px;
    background: #00ff00;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

/* تحسينات للوضع المظلم */
body.dark-mode .analytics-dashboard {
    background: #2d2d2d;
    border-left-color: #404040;
}

body.dark-mode .metric-card,
body.dark-mode .chart-card,
body.dark-mode .table-card,
body.dark-mode .performance-stats {
    background: #3a3a3a;
    border-color: #555555;
}

body.dark-mode .performance-item {
    background: #404040;
}

body.dark-mode .data-row {
    border-bottom-color: #555555;
}

/* تحسينات الاستجابة للأجهزة المحمولة */
@media (max-width: 768px) {
    .analytics-dashboard {
        width: 100%;
        right: -100%;
    }

    .quick-metrics {
        grid-template-columns: 1fr;
    }

    .performance-grid {
        grid-template-columns: 1fr;
    }



    .ai-assistant {
        width: 100%;
        left: 0;
        right: 0;
        bottom: 0;
        height: 70vh;
        border-radius: 15px 15px 0 0;
    }


}

/* أنماط نظام الأقسام المحسّن */
.enhanced-toc {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    margin: 20px auto;
    max-width: 1200px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.toc-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.toc-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.toc-controls {
    display: flex;
    gap: 10px;
}

.toc-toggle-all,
.toc-filter {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: var(--white);
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition);
}

.toc-toggle-all:hover,
.toc-filter:hover {
    background: rgba(255,255,255,0.3);
}

.toc-progress {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 15px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-color);
    min-width: 80px;
}

.toc-content {
    padding: 20px;
}

.toc-category {
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
    border-radius: 10px;
    overflow: hidden;
}

.category-header {
    background: #f8f9fa;
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-color);
}

.category-header:hover {
    background: #e9ecef;
}

.category-icon {
    font-size: 18px;
}

.category-name {
    flex: 1;
    font-weight: 600;
    color: var(--text-color);
}

.category-count {
    font-size: 12px;
    color: #666;
    background: #e0e0e0;
    padding: 2px 8px;
    border-radius: 10px;
}

.category-toggle {
    transition: transform 0.3s ease;
}

.toc-category.collapsed .category-toggle {
    transform: rotate(-90deg);
}

.category-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    padding: 20px;
    background: var(--white);
}

.toc-category.collapsed .category-sections {
    display: none;
}

.toc-item {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 15px;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.toc-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-color: var(--primary-color);
}

.toc-item.active {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #f8f9ff, #fff);
    box-shadow: 0 4px 15px rgba(69, 53, 205, 0.2);
}

.toc-item.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
}

.toc-item-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 10px;
}

.section-number {
    background: var(--primary-color);
    color: var(--white);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    flex-shrink: 0;
}

.section-title {
    flex: 1;
    font-weight: 600;
    color: var(--text-color);
    line-height: 1.4;
    margin-bottom: 8px;
}

.section-meta {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.reading-time,
.complexity-badge {
    font-size: 11px;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.reading-time {
    background: #e3f2fd;
    color: #1976d2;
}

.complexity-badge.complexity-beginner {
    background: #e8f5e8;
    color: #2e7d32;
}

.complexity-badge.complexity-intermediate {
    background: #fff3e0;
    color: #f57c00;
}

.complexity-badge.complexity-advanced {
    background: #ffebee;
    color: #c62828;
}

.toc-item-progress {
    display: flex;
    align-items: center;
    gap: 10px;
}

.item-progress-bar {
    flex: 1;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    overflow: hidden;
}

.item-progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
    border-radius: 2px;
}

.item-status {
    font-size: 11px;
    color: #666;
    min-width: 60px;
}

/* أنماط الأقسام المحسّنة */
.enhanced-section {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    margin: 25px auto;
    max-width: 1200px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: var(--transition);
}

.enhanced-section:hover {
    box-shadow: 0 6px 25px rgba(0,0,0,0.12);
}

.section-wrapper {
    position: relative;
}

.section-header-enhanced {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.section-meta-info {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.section-number-badge {
    background: var(--primary-color);
    color: var(--white);
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    flex-shrink: 0;
}

.section-details {
    flex: 1;
}

.section-title-enhanced {
    margin: 0 0 10px 0;
    color: var(--text-color);
    font-size: 22px;
    font-weight: 600;
    line-height: 1.3;
}

.section-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.category-badge,
.reading-time-badge {
    font-size: 12px;
    padding: 4px 10px;
    border-radius: 15px;
    font-weight: 500;
}

.category-badge {
    background: #e3f2fd;
    color: #1976d2;
}

.reading-time-badge {
    background: #f3e5f5;
    color: #7b1fa2;
}

.section-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-bookmark,
.section-share {
    background: none;
    border: 1px solid var(--border-color);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 16px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.section-bookmark:hover,
.section-share:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.section-toggle-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-toggle-btn:hover {
    background: var(--accent-color);
    transform: translateY(-1px);
}

.section-content-wrapper {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease;
}

.enhanced-section:not(.collapsed) .section-content-wrapper {
    max-height: none;
}

.content-progress-indicator {
    background: #f8f9fa;
    padding: 0;
    border-bottom: 1px solid var(--border-color);
}

.content-progress-bar {
    height: 4px;
    background: #e0e0e0;
}

.content-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
}

.section-content-enhanced {
    padding: 30px;
    line-height: 1.8;
    color: var(--text-color);
}

/* المتتبع العائم */
.floating-tracker {
    position: fixed;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 9997;
    min-width: 250px;
    transition: var(--transition);
}

.floating-tracker:hover {
    box-shadow: 0 6px 25px rgba(0,0,0,0.2);
}

.tracker-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.current-section {
    display: flex;
    align-items: center;
    gap: 8px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.section-indicator {
    font-size: 16px;
}

.current-title {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 14px;
    line-height: 1.3;
}

.navigation-arrows {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}

.nav-arrow {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 14px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-arrow:hover {
    background: var(--accent-color);
    transform: scale(1.1);
}

.nav-arrow:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.section-counter {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-color);
    text-align: center;
    min-width: 60px;
}

.tracker-progress {
    margin-top: 5px;
}

.mini-progress-bar {
    height: 6px;
    background: #e0e0e0;
    border-radius: 3px;
    overflow: hidden;
}

.mini-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
    border-radius: 3px;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .enhanced-toc {
        margin: 15px;
        border-radius: 10px;
    }

    .toc-header {
        padding: 15px;
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .toc-controls {
        justify-content: center;
    }

    .category-sections {
        grid-template-columns: 1fr;
        padding: 15px;
        gap: 10px;
    }

    .enhanced-section {
        margin: 15px;
        border-radius: 10px;
    }

    .section-header-enhanced {
        padding: 15px;
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .section-meta-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 10px;
    }

    .section-controls {
        justify-content: center;
    }

    .section-content-enhanced {
        padding: 20px;
    }

    .floating-tracker {
        left: 10px;
        right: 10px;
        min-width: auto;
        transform: translateY(-50%);
    }

    .current-title {
        font-size: 12px;
    }

    .navigation-arrows {
        gap: 8px;
    }

    .nav-arrow {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }
}

/* تحسينات للوضع المظلم */
body.dark-mode .enhanced-toc,
body.dark-mode .enhanced-section,
body.dark-mode .floating-tracker {
    background: #2d2d2d;
    border-color: #404040;
}

body.dark-mode .toc-progress,
body.dark-mode .category-header,
body.dark-mode .section-header-enhanced {
    background: #3a3a3a;
    border-color: #555555;
}

body.dark-mode .toc-item,
body.dark-mode .section-content-enhanced {
    background: #2d2d2d;
    border-color: #404040;
}

body.dark-mode .toc-item.active {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
}

body.dark-mode .progress-bar,
body.dark-mode .item-progress-bar,
body.dark-mode .content-progress-bar,
body.dark-mode .mini-progress-bar {
    background: #404040;
}

/* مؤشر التقدم في أعلى الصفحة */
.page-progress-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(0,0,0,0.1);
    z-index: 10003;
}

.page-progress-indicator .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.1s ease;
}

/* زر العودة للأعلى المحسّن */
.enhanced-back-to-top {
    position: fixed;
    bottom: 100px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: var(--white);
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9996;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.enhanced-back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.back-to-top-icon {
    font-size: 20px;
    position: relative;
    z-index: 2;
}

.back-to-top-progress {
    position: absolute;
    top: -2px;
    left: -2px;
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-color) 0deg, #e0e0e0 0deg);
    z-index: 1;
}

/* لوحة الإشارات المرجعية */
.bookmarks-panel {
    position: fixed;
    top: 50%;
    right: -350px;
    transform: translateY(-50%);
    width: 320px;
    max-height: 70vh;
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
    z-index: 10001;
    transition: right 0.3s ease;
    overflow: hidden;
}

.bookmarks-panel.open {
    right: 20px;
}

.bookmarks-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bookmarks-header h3 {
    margin: 0;
    font-size: 16px;
}

.bookmarks-close {
    background: none;
    border: none;
    color: var(--white);
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
}

.bookmarks-close:hover {
    background: rgba(255,255,255,0.2);
}

.bookmarks-content {
    padding: 20px;
    max-height: calc(70vh - 60px);
    overflow-y: auto;
}

.no-bookmarks {
    text-align: center;
    color: #666;
    font-style: italic;
    margin: 20px 0;
}

.bookmark-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 10px;
    background: #f8f9fa;
    transition: var(--transition);
}

.bookmark-item:hover {
    background: #e9ecef;
    border-color: var(--primary-color);
}

.bookmark-title {
    flex: 1;
    font-size: 14px;
    color: var(--text-color);
    line-height: 1.3;
}

.bookmark-go,
.bookmark-remove {
    padding: 6px 12px;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    transition: var(--transition);
}

.bookmark-go {
    background: var(--primary-color);
    color: var(--white);
}

.bookmark-go:hover {
    background: var(--accent-color);
}

.bookmark-remove {
    background: #dc3545;
    color: var(--white);
}

.bookmark-remove:hover {
    background: #c82333;
}



/* تحسينات إضافية للأقسام المحسّنة */
.legacy-section {
    transition: all 0.3s ease;
}

.legacy-section.reading {
    border-left: 4px solid var(--primary-color);
    background: linear-gradient(135deg, #f8f9ff, #fff);
}

/* تحسينات للوضع المظلم */
body.dark-mode .enhanced-back-to-top {
    background: #2d2d2d;
    border-color: var(--primary-color);
}

body.dark-mode .bookmarks-panel {
    background: #2d2d2d;
    border-color: #404040;
}

body.dark-mode .bookmarks-content {
    background: #2d2d2d;
}

body.dark-mode .bookmark-item {
    background: #3a3a3a;
    border-color: #555555;
}

body.dark-mode .bookmark-item:hover {
    background: #404040;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .bookmarks-panel {
        width: 280px;
        right: -300px;
    }

    .bookmarks-panel.open {
        right: 10px;
    }

    .enhanced-back-to-top {
        bottom: 80px;
        right: 15px;
        width: 50px;
        height: 50px;
    }

    .back-to-top-icon {
        font-size: 18px;
    }

    .back-to-top-progress {
        width: 54px;
        height: 54px;
    }



    .floating-tracker {
        left: 10px;
        right: 10px;
        min-width: auto;
        padding: 12px;
    }

    .tracker-content {
        gap: 8px;
    }

    .current-title {
        font-size: 12px;
    }

    .navigation-arrows {
        gap: 8px;
    }

    .nav-arrow {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }

    .section-counter {
        font-size: 11px;
        min-width: 50px;
    }
}

/* رسوم متحركة للتفاعل */
@keyframes bookmarkAdded {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.bookmark-added {
    animation: bookmarkAdded 0.3s ease;
}

@keyframes progressPulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.progress-pulse {
    animation: progressPulse 1s ease infinite;
}
