/* ملف CSS منفصل لتحسين الأداء */

/* متغيرات CSS للألوان والقيم */
:root {
    --primary-color: #4535cd;
    --secondary-color: #81cdf8;
    --accent-color: #3a2ba8;
    --background-color: #f5f5f5;
    --text-color: #333333;
    --white: #ffffff;
    --border-color: #dddddd;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* إعدادات عامة محسّنة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    scroll-behavior: smooth;
}

/* تحسينات الهيدر */
header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 20px;
    text-align: center;
    color: var(--white);
    position: relative;
    overflow: hidden;
}

header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

header h1 {
    margin: 0;
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    position: relative;
    z-index: 1;
}

/* تحسينات شريط التنقل */
nav {
    background-color: var(--white);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow);
}

nav .nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: auto;
    padding: 0 20px;
}

nav .logo {
    font-weight: bold;
    color: var(--primary-color);
    font-size: 1.2rem;
}

nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 10px;
}

nav ul li a {
    text-decoration: none;
    color: var(--primary-color);
    font-weight: 500;
    padding: 12px 16px;
    border-radius: 25px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

nav ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

nav ul li a:hover::before {
    left: 100%;
}

nav ul li a:hover,
nav ul li a.active {
    background-color: var(--secondary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(69, 53, 205, 0.3);
}

/* تحسينات الحاوي الرئيسي */
.container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 0 20px;
}

/* تحسينات الأقسام */
section {
    background-color: var(--white);
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid transparent;
}

section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: var(--secondary-color);
}

section h2 {
    margin: 0;
    padding: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

section h2::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s;
}

section h2:hover::before {
    left: 100%;
}

section h2:hover {
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
}

section .content {
    display: none;
    padding: 25px;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

section.expanded .content {
    display: block;
}

/* تحسينات الجداول */
.table-container {
    overflow-x: auto;
    margin: 20px 0;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--white);
    font-size: 14px;
}

table th {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    padding: 15px 12px;
    text-align: right;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 2;
    border-bottom: 2px solid var(--accent-color);
}

table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

table tbody tr:nth-child(odd) {
    background-color: #fafafa;
}

table tbody tr:hover {
    background-color: #f0f8ff;
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* تحسينات شريط البحث */
.search-filter-container {
    background: linear-gradient(135deg, var(--white), #f8f9fa);
    padding: 25px;
    margin-bottom: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    align-items: center;
    border: 1px solid var(--border-color);
}

.search-box,
.filter-select {
    padding: 12px 18px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 14px;
    transition: var(--transition);
    background-color: var(--white);
}

.search-box {
    flex: 1;
    min-width: 250px;
}

.search-box:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(69, 53, 205, 0.1);
    transform: translateY(-1px);
}

/* تحسينات الاستجابة للأجهزة المحمولة */
@media (max-width: 768px) {
    .search-filter-container {
        flex-direction: column;
        align-items: stretch;
        padding: 20px;
    }
    
    .search-box,
    .filter-select {
        min-width: 100%;
        margin-bottom: 10px;
    }
    
    nav ul {
        position: absolute;
        top: 100%;
        right: 0;
        background-color: var(--white);
        width: 250px;
        flex-direction: column;
        border: 1px solid var(--border-color);
        border-radius: 0 0 var(--border-radius) var(--border-radius);
        display: none;
        box-shadow: var(--shadow);
    }
    
    nav ul.show {
        display: flex;
    }
    
    nav ul li {
        margin: 0;
    }
    
    nav ul li a {
        display: block;
        padding: 15px 20px;
        border-radius: 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .menu-toggle {
        display: flex;
        flex-direction: column;
        cursor: pointer;
        padding: 5px;
    }
    
    .menu-toggle span {
        width: 25px;
        height: 3px;
        background-color: var(--primary-color);
        margin: 3px 0;
        transition: var(--transition);
        border-radius: 2px;
    }
    
    .menu-toggle.open span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }
    
    .menu-toggle.open span:nth-child(2) {
        opacity: 0;
    }
    
    .menu-toggle.open span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }
    
    table {
        font-size: 12px;
        min-width: 600px;
    }
    
    table th,
    table td {
        padding: 8px 6px;
        word-wrap: break-word;
    }
    
    section h2 {
        padding: 15px;
        font-size: 1rem;
    }
    
    section .content {
        padding: 20px;
    }
}

/* تحسينات إضافية للأداء */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تحسينات الطباعة */
@media print {
    nav,
    #backToTop,
    .search-filter-container,
    .loading-indicator {
        display: none !important;
    }

    section .content {
        display: block !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: black;
        background: white;
    }

    section {
        break-inside: avoid;
        margin-bottom: 20pt;
    }

    table {
        break-inside: avoid;
    }
}

/* تحسينات إضافية للأداء والتفاعل */
.toggle-icon {
    transition: transform 0.3s ease;
    display: inline-block;
}

.expanded .toggle-icon {
    transform: rotate(90deg);
}

/* تأثيرات التحميل */
.loading-indicator {
    display: none;
    text-align: center;
    padding: 20px;
    color: var(--primary-color);
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات زر العودة للأعلى */
#backToTop {
    display: none;
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    font-size: 20px;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(69, 53, 205, 0.3);
    z-index: 1000;
}

#backToTop:hover {
    background: var(--accent-color);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(69, 53, 205, 0.4);
}

/* تحسينات إضافية للجداول */
table tbody tr {
    transition: all 0.2s ease;
}

table tbody tr:hover {
    background-color: #e3f2fd !important;
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* تحسينات النصوص المميزة */
mark {
    background-color: #ffeb3b;
    color: #333;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

/* تحسينات الروابط */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--accent-color);
    text-decoration: underline;
}

/* تحسينات الكود */
pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 15px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
}

code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

/* تحسينات القوائم */
ul, ol {
    padding-right: 20px;
}

li {
    margin-bottom: 8px;
    line-height: 1.6;
}

/* تحسينات العناوين الفرعية */
h3 {
    color: var(--primary-color);
    margin-top: 25px;
    margin-bottom: 15px;
    font-size: 1.3em;
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 5px;
}

/* تحسينات الفقرات */
p {
    margin-bottom: 15px;
    text-align: justify;
}

/* تحسينات النصوص المهمة */
strong {
    color: var(--primary-color);
    font-weight: 600;
}

em {
    color: var(--accent-color);
    font-style: italic;
}

/* تحسينات الحدود والظلال */
.card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 20px;
    margin-bottom: 20px;
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

/* تحسينات الأزرار */
.btn {
    display: inline-block;
    padding: 12px 24px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    text-decoration: none;
}

.btn:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(69, 53, 205, 0.3);
}

.btn-secondary {
    background: var(--secondary-color);
}

.btn-secondary:hover {
    background: #6bb6ff;
}

/* تحسينات الرسائل والتنبيهات */
.alert {
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border-left: 4px solid;
}

.alert-info {
    background-color: #d1ecf1;
    border-left-color: #17a2b8;
    color: #0c5460;
}

.alert-success {
    background-color: #d4edda;
    border-left-color: #28a745;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    border-left-color: #ffc107;
    color: #856404;
}

.alert-error {
    background-color: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
}
