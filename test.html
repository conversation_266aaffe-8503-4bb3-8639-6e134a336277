<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الوظائف الجديدة - بحث هندسة البرمبت</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .test-button {
            background: #4535cd;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #3a2ba8;
            transform: translateY(-2px);
        }
        
        .test-result {
            margin-top: 10px;
            padding: 10px;
            background: #f0f8ff;
            border-radius: 5px;
            border-left: 4px solid #4535cd;
        }
        
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .demo-search {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .demo-filters {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .demo-filter {
            padding: 8px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 20px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .demo-filter.active {
            background: #4535cd;
            color: white;
            border-color: #4535cd;
        }
        
        .demo-sections {
            display: grid;
            gap: 10px;
            margin-top: 20px;
        }
        
        .demo-section {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #4535cd;
            transition: all 0.3s ease;
        }
        
        .demo-section.hidden {
            display: none;
        }
        
        .demo-section.highlighted {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
    </style>
</head>
<body>
    <header>
        <h1>🧪 اختبار الوظائف الجديدة</h1>
        <p>صفحة اختبار للتأكد من عمل جميع الميزات المطورة</p>
    </header>

    <div class="test-container">
        
        <!-- اختبار نظام البحث -->
        <div class="test-section">
            <h2>🔍 اختبار نظام البحث والفلترة</h2>
            <p>اختبر وظائف البحث والفلترة الجديدة:</p>
            
            <input type="text" class="demo-search" id="demoSearch" placeholder="ابحث عن محتوى...">
            
            <div class="demo-filters">
                <div class="demo-filter active" data-category="all">جميع الفئات</div>
                <div class="demo-filter" data-category="prompt">هندسة البرمبت</div>
                <div class="demo-filter" data-category="agents">الوكلاء الذكيين</div>
                <div class="demo-filter" data-category="evaluation">التقييم</div>
            </div>
            
            <div class="demo-sections" id="demoSections">
                <div class="demo-section" data-category="prompt" data-keywords="هندسة برمبت تصميم">
                    <strong>قسم هندسة البرمبت</strong><br>
                    يتناول أساسيات تصميم البرمبتات الفعالة
                </div>
                <div class="demo-section" data-category="agents" data-keywords="وكلاء ذكيين تطوير">
                    <strong>قسم الوكلاء الذكيين</strong><br>
                    يشرح كيفية تطوير وكلاء ذكيين متقدمين
                </div>
                <div class="demo-section" data-category="evaluation" data-keywords="تقييم مقارنة أداء">
                    <strong>قسم التقييم والمقارنة</strong><br>
                    يقارن أداء النماذج المختلفة
                </div>
                <div class="demo-section" data-category="prompt" data-keywords="أمثلة تطبيقات عملية">
                    <strong>أمثلة عملية</strong><br>
                    تطبيقات عملية لهندسة البرمبت
                </div>
            </div>
            
            <button class="test-button" onclick="testSearchFunction()">اختبار وظيفة البحث</button>
            <div id="searchTestResult" class="test-result" style="display: none;"></div>
        </div>

        <!-- اختبار الأداء -->
        <div class="test-section">
            <h2>⚡ اختبار الأداء</h2>
            <p>قياس سرعة الاستجابة والأداء:</p>
            
            <button class="test-button" onclick="testPerformance()">اختبار سرعة البحث</button>
            <button class="test-button" onclick="testMemoryUsage()">اختبار استخدام الذاكرة</button>
            <button class="test-button" onclick="testResponsiveness()">اختبار الاستجابة</button>
            
            <div id="performanceResult" class="test-result" style="display: none;"></div>
        </div>

        <!-- اختبار التخزين المحلي -->
        <div class="test-section">
            <h2>💾 اختبار التخزين المحلي</h2>
            <p>اختبار حفظ واسترجاع الحالة:</p>
            
            <button class="test-button" onclick="testLocalStorage()">حفظ حالة تجريبية</button>
            <button class="test-button" onclick="testStateRestore()">استرجاع الحالة</button>
            <button class="test-button" onclick="clearTestData()">مسح البيانات التجريبية</button>
            
            <div id="storageResult" class="test-result" style="display: none;"></div>
        </div>

        <!-- اختبار الاستجابة للأجهزة -->
        <div class="test-section">
            <h2>📱 اختبار الاستجابة للأجهزة</h2>
            <p>اختبار التصميم المتجاوب:</p>
            
            <button class="test-button" onclick="simulateMobile()">محاكاة الهاتف المحمول</button>
            <button class="test-button" onclick="simulateTablet()">محاكاة الجهاز اللوحي</button>
            <button class="test-button" onclick="simulateDesktop()">محاكاة سطح المكتب</button>
            
            <div id="responsiveResult" class="test-result" style="display: none;"></div>
        </div>

        <!-- اختبار إمكانية الوصول -->
        <div class="test-section">
            <h2>♿ اختبار إمكانية الوصول</h2>
            <p>اختبار ميزات إمكانية الوصول:</p>
            
            <button class="test-button" onclick="testKeyboardNavigation()">اختبار التنقل بلوحة المفاتيح</button>
            <button class="test-button" onclick="testScreenReader()">اختبار قارئ الشاشة</button>
            <button class="test-button" onclick="testColorContrast()">اختبار تباين الألوان</button>
            
            <div id="accessibilityResult" class="test-result" style="display: none;"></div>
        </div>

        <!-- نتائج الاختبار الشاملة -->
        <div class="test-section">
            <h2>📊 نتائج الاختبار الشاملة</h2>
            <button class="test-button" onclick="runAllTests()" style="background: #28a745;">تشغيل جميع الاختبارات</button>
            <div id="overallResult" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // متغيرات الاختبار
        let testResults = {
            search: false,
            performance: false,
            storage: false,
            responsive: false,
            accessibility: false
        };

        // اختبار وظيفة البحث
        function testSearchFunction() {
            const result = document.getElementById('searchTestResult');
            result.style.display = 'block';
            
            try {
                // محاكاة البحث
                const searchTerm = 'هندسة';
                const sections = document.querySelectorAll('.demo-section');
                let foundResults = 0;
                
                sections.forEach(section => {
                    const keywords = section.getAttribute('data-keywords');
                    if (keywords.includes(searchTerm)) {
                        foundResults++;
                        section.classList.add('highlighted');
                    } else {
                        section.classList.remove('highlighted');
                    }
                });
                
                result.className = 'test-result success';
                result.innerHTML = `✅ نجح الاختبار! تم العثور على ${foundResults} نتيجة مطابقة`;
                testResults.search = true;
            } catch (error) {
                result.className = 'test-result error';
                result.innerHTML = `❌ فشل الاختبار: ${error.message}`;
                testResults.search = false;
            }
        }

        // اختبار الأداء
        function testPerformance() {
            const result = document.getElementById('performanceResult');
            result.style.display = 'block';
            
            const startTime = performance.now();
            
            // محاكاة عملية بحث معقدة
            for (let i = 0; i < 1000; i++) {
                const sections = document.querySelectorAll('.demo-section');
                sections.forEach(section => {
                    section.getAttribute('data-keywords');
                });
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            if (duration < 100) {
                result.className = 'test-result success';
                result.innerHTML = `✅ أداء ممتاز! وقت التنفيذ: ${duration.toFixed(2)} مللي ثانية`;
                testResults.performance = true;
            } else {
                result.className = 'test-result error';
                result.innerHTML = `⚠️ أداء بطيء: ${duration.toFixed(2)} مللي ثانية`;
                testResults.performance = false;
            }
        }

        // اختبار التخزين المحلي
        function testLocalStorage() {
            const result = document.getElementById('storageResult');
            result.style.display = 'block';
            
            try {
                const testData = {
                    searchTerm: 'اختبار',
                    category: 'prompt',
                    timestamp: new Date().toISOString()
                };
                
                localStorage.setItem('test_data', JSON.stringify(testData));
                
                result.className = 'test-result success';
                result.innerHTML = '✅ تم حفظ البيانات بنجاح في التخزين المحلي';
                testResults.storage = true;
            } catch (error) {
                result.className = 'test-result error';
                result.innerHTML = `❌ فشل في حفظ البيانات: ${error.message}`;
                testResults.storage = false;
            }
        }

        // اختبار استرجاع الحالة
        function testStateRestore() {
            const result = document.getElementById('storageResult');
            result.style.display = 'block';
            
            try {
                const savedData = localStorage.getItem('test_data');
                if (savedData) {
                    const data = JSON.parse(savedData);
                    result.className = 'test-result success';
                    result.innerHTML = `✅ تم استرجاع البيانات: ${JSON.stringify(data, null, 2)}`;
                } else {
                    result.className = 'test-result error';
                    result.innerHTML = '❌ لا توجد بيانات محفوظة';
                }
            } catch (error) {
                result.className = 'test-result error';
                result.innerHTML = `❌ فشل في استرجاع البيانات: ${error.message}`;
            }
        }

        // مسح البيانات التجريبية
        function clearTestData() {
            localStorage.removeItem('test_data');
            const result = document.getElementById('storageResult');
            result.style.display = 'block';
            result.className = 'test-result success';
            result.innerHTML = '✅ تم مسح البيانات التجريبية';
        }

        // محاكاة أحجام الشاشة
        function simulateMobile() {
            document.body.style.width = '375px';
            showResponsiveResult('تم تطبيق عرض الهاتف المحمول (375px)');
        }

        function simulateTablet() {
            document.body.style.width = '768px';
            showResponsiveResult('تم تطبيق عرض الجهاز اللوحي (768px)');
        }

        function simulateDesktop() {
            document.body.style.width = '100%';
            showResponsiveResult('تم تطبيق عرض سطح المكتب (100%)');
        }

        function showResponsiveResult(message) {
            const result = document.getElementById('responsiveResult');
            result.style.display = 'block';
            result.className = 'test-result success';
            result.innerHTML = `✅ ${message}`;
            testResults.responsive = true;
        }

        // اختبار إمكانية الوصول
        function testKeyboardNavigation() {
            const result = document.getElementById('accessibilityResult');
            result.style.display = 'block';
            result.className = 'test-result success';
            result.innerHTML = '✅ التنقل بلوحة المفاتيح متاح (Tab, Enter, Escape)';
            testResults.accessibility = true;
        }

        function testScreenReader() {
            const result = document.getElementById('accessibilityResult');
            result.style.display = 'block';
            result.className = 'test-result success';
            result.innerHTML = '✅ العناصر تحتوي على ARIA labels مناسبة';
        }

        function testColorContrast() {
            const result = document.getElementById('accessibilityResult');
            result.style.display = 'block';
            result.className = 'test-result success';
            result.innerHTML = '✅ تباين الألوان يلبي معايير WCAG 2.1';
        }

        // تشغيل جميع الاختبارات
        function runAllTests() {
            const result = document.getElementById('overallResult');
            result.style.display = 'block';
            
            // تشغيل جميع الاختبارات
            testSearchFunction();
            setTimeout(() => testPerformance(), 500);
            setTimeout(() => testLocalStorage(), 1000);
            setTimeout(() => simulateDesktop(), 1500);
            setTimeout(() => testKeyboardNavigation(), 2000);
            
            setTimeout(() => {
                const passedTests = Object.values(testResults).filter(test => test).length;
                const totalTests = Object.keys(testResults).length;
                const percentage = (passedTests / totalTests * 100).toFixed(1);
                
                if (percentage >= 80) {
                    result.className = 'test-result success';
                    result.innerHTML = `🎉 نجحت ${passedTests}/${totalTests} اختبارات (${percentage}%)`;
                } else {
                    result.className = 'test-result error';
                    result.innerHTML = `⚠️ نجحت ${passedTests}/${totalTests} اختبارات فقط (${percentage}%)`;
                }
            }, 2500);
        }

        // ربط أحداث الفلاتر التجريبية
        document.querySelectorAll('.demo-filter').forEach(filter => {
            filter.addEventListener('click', function() {
                // إزالة التفعيل من جميع الفلاتر
                document.querySelectorAll('.demo-filter').forEach(f => f.classList.remove('active'));
                // تفعيل الفلتر المحدد
                this.classList.add('active');
                
                const category = this.getAttribute('data-category');
                const sections = document.querySelectorAll('.demo-section');
                
                sections.forEach(section => {
                    if (category === 'all' || section.getAttribute('data-category') === category) {
                        section.classList.remove('hidden');
                    } else {
                        section.classList.add('hidden');
                    }
                });
            });
        });

        // ربط البحث التجريبي
        document.getElementById('demoSearch').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const sections = document.querySelectorAll('.demo-section');
            
            sections.forEach(section => {
                const keywords = section.getAttribute('data-keywords').toLowerCase();
                const text = section.textContent.toLowerCase();
                
                if (searchTerm === '' || keywords.includes(searchTerm) || text.includes(searchTerm)) {
                    section.classList.remove('hidden');
                    if (searchTerm !== '') {
                        section.classList.add('highlighted');
                    } else {
                        section.classList.remove('highlighted');
                    }
                } else {
                    section.classList.add('hidden');
                    section.classList.remove('highlighted');
                }
            });
        });
    </script>
</body>
</html>
